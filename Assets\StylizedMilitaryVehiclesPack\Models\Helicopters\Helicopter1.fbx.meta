fileFormatVersion: 2
guid: 271446cb54a82994d9cd5c20a7b8f715
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: back_screw
    100002: body
    100004: gun
    100006: //RootNode
    100008: main_screw
    100010: Rocket_l
    100012: Rocket_l.001
    100014: Rocket_l.002
    100016: Rocket_l.003
    100018: Rocket_l.004
    100020: Rocket_l.005
    100022: Rocket_l.006
    100024: Rocket_l.007
    400000: back_screw
    400002: body
    400004: gun
    400006: //RootNode
    400008: main_screw
    400010: Rocket_l
    400012: Rocket_l.001
    400014: Rocket_l.002
    400016: Rocket_l.003
    400018: Rocket_l.004
    400020: Rocket_l.005
    400022: Rocket_l.006
    400024: Rocket_l.007
    2100000: Palette
    2300000: back_screw
    2300002: body
    2300004: gun
    2300006: main_screw
    2300008: Rocket_l
    2300010: Rocket_l.001
    2300012: Rocket_l.002
    2300014: Rocket_l.003
    2300016: Rocket_l.004
    2300018: Rocket_l.005
    2300020: Rocket_l.006
    2300022: Rocket_l.007
    3300000: back_screw
    3300002: body
    3300004: gun
    3300006: main_screw
    3300008: Rocket_l
    3300010: Rocket_l.001
    3300012: Rocket_l.002
    3300014: Rocket_l.003
    3300016: Rocket_l.004
    3300018: Rocket_l.005
    3300020: Rocket_l.006
    3300022: Rocket_l.007
    4300000: Rocket_l.007
    4300002: Rocket_l.006
    4300004: Rocket_l.005
    4300006: Rocket_l.004
    4300008: Rocket_l.003
    4300010: Rocket_l.002
    4300012: Rocket_l.001
    4300014: Rocket_l
    4300016: gun
    4300018: back_screw
    4300020: main_screw
    4300022: body
  externalObjects: {}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 122675
  packageName: Stylized Military Vehicles Pack
  packageVersion: 1.02
  assetPath: Assets/StylizedMilitaryVehiclesPack/Models/Helicopters/Helicopter1.fbx
  uploadId: 338346
