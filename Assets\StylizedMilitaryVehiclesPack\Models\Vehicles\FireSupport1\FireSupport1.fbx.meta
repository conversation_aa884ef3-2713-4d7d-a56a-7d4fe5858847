fileFormatVersion: 2
guid: 3b1b9bb6593e27a4da0eb0b8ce7443fd
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: Armature
    100004: Body
    100006: FL1_b
    100008: FL1_b_end
    100010: FL2
    100012: FL2_b
    100014: FL2_b_end
    100016: FL3
    100018: FL3_b
    100020: FL3_b_end
    100022: FL4
    100024: FL4_b
    100026: FL4_b_end
    100028: FL5
    100030: FL5_b
    100032: FL5_b_end
    100034: FL6
    100036: FL6_b
    100038: FL6_b_end
    100040: FL7
    100042: FL7_b
    100044: FL7_b_end
    100046: FL8
    100048: FL8_b
    100050: FL8_b_end
    100052: FR1
    100054: FR1_b
    100056: FR1_b_end
    100058: FR2
    100060: FR2_b
    100062: FR2_b_end
    100064: FR3
    100066: FR3_b
    100068: FR3_b_end
    100070: FR4
    100072: FR4_b
    100074: FR4_b_end
    100076: FR5
    100078: FR5_b
    100080: FR5_b_end
    100082: FR6
    100084: FR6_b
    100086: FR6_b_end
    100088: FR7
    100090: FR7_b
    100092: FR7_b_end
    100094: FR8
    100096: FR8_b
    100098: FR8_b_end
    100100: Gun
    100102: root
    100104: Trucks
    100106: Turret
    100108: FL1
    400000: //RootNode
    400002: Armature
    400004: Body
    400006: FL1_b
    400008: FL1_b_end
    400010: FL2
    400012: FL2_b
    400014: FL2_b_end
    400016: FL3
    400018: FL3_b
    400020: FL3_b_end
    400022: FL4
    400024: FL4_b
    400026: FL4_b_end
    400028: FL5
    400030: FL5_b
    400032: FL5_b_end
    400034: FL6
    400036: FL6_b
    400038: FL6_b_end
    400040: FL7
    400042: FL7_b
    400044: FL7_b_end
    400046: FL8
    400048: FL8_b
    400050: FL8_b_end
    400052: FR1
    400054: FR1_b
    400056: FR1_b_end
    400058: FR2
    400060: FR2_b
    400062: FR2_b_end
    400064: FR3
    400066: FR3_b
    400068: FR3_b_end
    400070: FR4
    400072: FR4_b
    400074: FR4_b_end
    400076: FR5
    400078: FR5_b
    400080: FR5_b_end
    400082: FR6
    400084: FR6_b
    400086: FR6_b_end
    400088: FR7
    400090: FR7_b
    400092: FR7_b_end
    400094: FR8
    400096: FR8_b
    400098: FR8_b_end
    400100: Gun
    400102: root
    400104: Trucks
    400106: Turret
    400108: FL1
    2100000: Palette
    2100002: Trucks
    2300000: //RootNode
    2300002: Body
    2300004: FL2
    2300006: FL3
    2300008: FL4
    2300010: FL5
    2300012: FL6
    2300014: FL7
    2300016: FL8
    2300018: FR1
    2300020: FR2
    2300022: FR3
    2300024: FR4
    2300026: FR5
    2300028: FR6
    2300030: FR7
    2300032: FR8
    2300034: Gun
    2300036: Trucks
    2300038: Turret
    2300040: FL1
    3300000: //RootNode
    3300002: Body
    3300004: FL2
    3300006: FL3
    3300008: FL4
    3300010: FL5
    3300012: FL6
    3300014: FL7
    3300016: FL8
    3300018: FR1
    3300020: FR2
    3300022: FR3
    3300024: FR4
    3300026: FR5
    3300028: FR6
    3300030: FR7
    3300032: FR8
    3300034: Gun
    3300036: Trucks
    3300038: Turret
    3300040: FL1
    4300000: Body
    4300002: Trucks
    4300004: FR8
    4300006: FR7
    4300008: FR6
    4300010: FR5
    4300012: FR4
    4300014: FR3
    4300016: FR2
    4300018: FR1
    4300020: FL8
    4300022: FL7
    4300024: FL6
    4300026: FL5
    4300028: FL4
    4300030: FL3
    4300032: FL2
    4300034: Gun
    4300036: Turret
    4300038: FL1
    6400000: Body
    6400002: FL1
    6400004: FL2
    6400006: FL3
    6400008: FL4
    6400010: FL5
    6400012: FL6
    6400014: FL7
    6400016: FL8
    6400018: FR1
    6400020: FR2
    6400022: FR3
    6400024: FR4
    6400026: FR5
    6400028: FR6
    6400030: FR7
    6400032: FR8
    6400034: Gun
    6400036: Turret
    9500000: //RootNode
    13700000: Trucks
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 122675
  packageName: Stylized Military Vehicles Pack
  packageVersion: 1.02
  assetPath: Assets/StylizedMilitaryVehiclesPack/Models/Vehicles/FireSupport1/FireSupport1.fbx
  uploadId: 338346
