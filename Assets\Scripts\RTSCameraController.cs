using UnityEngine;
#if ENABLE_INPUT_SYSTEM
using UnityEngine.InputSystem;
#endif

// ColdVor - RTS-style camera controller for Unity 6
// Attach to Main Camera. Creates a pivot at runtime and orbits/zooms around it.
// Supports: WASD/Arrows, edge panning, MMB drag-pan, RMB rotate (yaw/pitch), scroll zoom,
// zoom-speed scaling, height clamping above ground, optional world bounds.

[DisallowMultipleComponent]
[RequireComponent(typeof(Camera))]
public class RTSCameraController : MonoBehaviour
{
    [Header("Pan / Translation")]
    [Tooltip("Base ground-plane pan speed in units per second.")]
    public float panSpeed = 20f;
    [Tooltip("Hold to pan faster.")]
    public KeyCode fastPanModifier = KeyCode.LeftShift;
    public float fastPanMultiplier = 2f;

    [Header("Edge Panning")]
    public bool edgePanning = true;
    [Tooltip("Thickness in pixels along screen edges to trigger panning.")]
    public int edgeThickness = 12;

    [Header("Rotate (hold RMB)")]
    public float rotateSpeed = 120f; // degrees/sec per mouse unit
    public float pitchSpeed = 120f;  // degrees/sec per mouse unit
    public float minPitch = 20f;
    public float maxPitch = 85f;
    public bool invertRotateX = false;
    public bool invertRotateY = true;

    [Header("Zoom (Scroll Wheel)")]
    public float zoomSpeed = 200f;        // units per scroll
    public float minDistance = 6f;        // min distance from pivot
    public float maxDistance = 120f;      // max distance from pivot
    public float zoomSmoothing = 0.08f;   // smaller = snappier

    [Header("Smoothing")]
    public float moveSmoothing = 0.08f;   // smaller = snappier
    public float rotateSmoothing = 0.08f; // smaller = snappier

    [Header("Ground & Height")]
    [Tooltip("Layers considered ground for height clamping and initial pivot raycasts.")]
    public LayerMask groundMask = ~0;
    [Tooltip("Minimum clearance the camera should keep above ground (in meters).")]
    public float minHeightAboveGround = 1.5f;

    [Header("World Bounds (optional)")]
    public bool useWorldBounds = false;
    public Vector2 worldXLimits = new Vector2(-500f, 500f);
    public Vector2 worldZLimits = new Vector2(-500f, 500f);

    [Header("Input Options")]
    public bool allowArrowKeys = true;
    public bool allowWASD = true;
    public bool allowMMBPan = true; // hold Middle Mouse to drag-pan

    // Internal state
    private Transform _pivot;            // parent of camera
    private Camera _cam;
    private float _yaw;                  // degrees
    private float _pitch;                // degrees
    private float _distance;             // camera local -Z

    // Targets for smoothing
    private Vector3 _targetPivotPos;
    private float _targetYaw;
    private float _targetPitch;
    private float _targetDistance;

    // SmoothDamp velocity caches
    private Vector3 _pivotVel;
    private float _yawVel;
    private float _pitchVel;
    private float _distVel;

    void Awake()
    {
        _cam = GetComponent<Camera>();
        EnsurePivot();
        InitializeFromCurrentView();
    }

    void EnsurePivot()
    {
        // Create a pivot GameObject (parent of camera) if needed
        if (transform.parent == null || transform.parent == transform)
        {
            var pivotGO = new GameObject("RTSCameraPivot");
            pivotGO.transform.position = transform.position;
            pivotGO.transform.rotation = Quaternion.identity;
            _pivot = pivotGO.transform;
            _pivot.gameObject.hideFlags = HideFlags.DontSave;
            transform.SetParent(_pivot, true);
        }
        else
        {
            _pivot = transform.parent;
        }
    }

    void InitializeFromCurrentView()
    {
        // Find a reasonable pivot target: raycast from screen center to ground; fallback to y=0 plane
        Vector3 pivotPos = _pivot.position;
        if (TryScreenCenterGround(out Vector3 hitPoint))
        {
            pivotPos = hitPoint;
        }
        else
        {
            // Intersect with y=0 plane
            var ray = new Ray(transform.position, transform.forward);
            if (new Plane(Vector3.up, Vector3.zero).Raycast(ray, out float enter))
                pivotPos = ray.origin + ray.direction * enter;
            else
                pivotPos = transform.position + transform.forward * 10f;
        }

        _pivot.position = pivotPos;
        _targetPivotPos = pivotPos;

        // Compute yaw/pitch from camera -> pivot vector
        Vector3 camOffset = transform.position - _pivot.position;
        _distance = camOffset.magnitude;
        if (_distance < 0.001f) _distance = Mathf.Clamp(_distance, minDistance, maxDistance);
        _targetDistance = Mathf.Clamp(_distance, minDistance, maxDistance);

        Vector3 dir = camOffset.normalized;
        _yaw = _targetYaw = Mathf.Atan2(dir.x, dir.z) * Mathf.Rad2Deg; // around Y
        _pitch = _targetPitch = Mathf.Clamp(Mathf.Asin(dir.y) * Mathf.Rad2Deg + 90f - 90f + (90f - 90f), minPitch, maxPitch);
        // A simpler way: pitch from horizontal
        _pitch = _targetPitch = Mathf.Clamp(Mathf.Acos(Mathf.Clamp(Vector3.Dot(Vector3.up, -dir), -1f, 1f)) * Mathf.Rad2Deg, minPitch, maxPitch);

        ApplyTransformImmediate();
    }

    void Update()
    {
        float dt = Time.unscaledDeltaTime; // camera should remain responsive even if timeScale changes

        // 1) Input -> desired deltas
        Vector3 panDelta = ReadPanInput(dt);
        ReadRotateInput(dt, out float dYaw, out float dPitch);
        float dZoom = ReadZoomInput();

        // 2) Apply to targets
        _targetPivotPos += panDelta;
        _targetYaw += dYaw;
        _targetPitch = Mathf.Clamp(_targetPitch + dPitch, minPitch, maxPitch);
        _targetDistance = Mathf.Clamp(_targetDistance - dZoom * zoomSpeed, minDistance, maxDistance);

        // 3) Optional world bounds (clamp pivot)
        if (useWorldBounds)
        {
            _targetPivotPos.x = Mathf.Clamp(_targetPivotPos.x, worldXLimits.x, worldXLimits.y);
            _targetPivotPos.z = Mathf.Clamp(_targetPivotPos.z, worldZLimits.x, worldZLimits.y);
        }

        // 4) Smooth towards targets
        _yaw = Mathf.SmoothDampAngle(_yaw, _targetYaw, ref _yawVel, rotateSmoothing, Mathf.Infinity, dt);
        _pitch = Mathf.SmoothDamp(_pitch, _targetPitch, ref _pitchVel, rotateSmoothing, Mathf.Infinity, dt);
        _distance = Mathf.SmoothDamp(_distance, _targetDistance, ref _distVel, zoomSmoothing, Mathf.Infinity, dt);
        _pivot.position = Vector3.SmoothDamp(_pivot.position, _targetPivotPos, ref _pivotVel, moveSmoothing, Mathf.Infinity, dt);

        // 5) Apply transforms
        ApplyTransformImmediate();

        // 6) Height clamping above ground (post-apply)
        EnforceGroundClearance();
    }

    void ApplyTransformImmediate()
    {
        _pivot.rotation = Quaternion.Euler(0f, _yaw, 0f) * Quaternion.Euler(_pitch, 0f, 0f);
        transform.localPosition = new Vector3(0f, 0f, -_distance);
        transform.localRotation = Quaternion.identity; // camera faces -Z of pivot rotation
    }

    Vector3 ReadPanInput(float dt)
    {
        Vector3 delta = Vector3.zero;

        // Keyboard pan
        Vector2 kb = Vector2.zero;
        if (allowWASD)
        {
            kb.x += IsKeyHeld(KeyCode.D) ? 1f : 0f;
            kb.x -= IsKeyHeld(KeyCode.A) ? 1f : 0f;
            kb.y += IsKeyHeld(KeyCode.W) ? 1f : 0f;
            kb.y -= IsKeyHeld(KeyCode.S) ? 1f : 0f;
        }
        if (allowArrowKeys)
        {
            kb.x += IsKeyHeld(KeyCode.RightArrow) ? 1f : 0f;
            kb.x -= IsKeyHeld(KeyCode.LeftArrow) ? 1f : 0f;
            kb.y += IsKeyHeld(KeyCode.UpArrow) ? 1f : 0f;
            kb.y -= IsKeyHeld(KeyCode.DownArrow) ? 1f : 0f;
        }
        kb = Vector2.ClampMagnitude(kb, 1f);

        // Edge panning (only when not rotating/MMB-dragging)
        if (edgePanning && !IsRMBHeld() && !IsMMBHeld())
        {
            Vector2 mp = GetMousePosition();
            if (mp.x <= edgeThickness) kb.x -= 1f;
            else if (mp.x >= Screen.width - edgeThickness) kb.x += 1f;
            if (mp.y <= edgeThickness) kb.y -= 1f;
            else if (mp.y >= Screen.height - edgeThickness) kb.y += 1f;
        }

        // Translate in camera-space projected onto XZ ground
        if (kb.sqrMagnitude > 0f)
        {
            Vector3 fwd = _pivot.forward; fwd.y = 0f; fwd.Normalize();
            Vector3 right = _pivot.right; right.y = 0f; right.Normalize();
            float speed = panSpeed * (IsKeyHeld(fastPanModifier) ? fastPanMultiplier : 1f);
            // scale speed by zoom so farther out pans a bit faster
            float zoomFactor = Mathf.Lerp(0.6f, 1.6f, Mathf.InverseLerp(minDistance, maxDistance, _targetDistance));
            delta += (right * kb.x + fwd * kb.y) * speed * zoomFactor * dt;
        }

        // MMB drag pan
        if (allowMMBPan && IsMMBHeld())
        {
            Vector2 md = GetMouseDelta();
            if (md.sqrMagnitude > 0f)
            {
                // Drag in screen space -> move along ground plane relative to camera
                Vector3 right = _pivot.right; right.y = 0f; right.Normalize();
                Vector3 fwd = _pivot.forward; fwd.y = 0f; fwd.Normalize();
                float dragScale = panSpeed * 0.03f * Mathf.Max(0.5f, _targetDistance * 0.02f);
                delta += (-right * md.x + -fwd * md.y) * dragScale; // drag feels like "grabbing" the world
            }
        }

        return delta;
    }

    void ReadRotateInput(float dt, out float dYaw, out float dPitch)
    {
        dYaw = 0f; dPitch = 0f;
        if (!IsRMBHeld()) return;
        Vector2 md = GetMouseDelta();
        if (md.sqrMagnitude <= 0f) return;
        float sx = invertRotateX ? -1f : 1f;
        float sy = invertRotateY ? -1f : 1f;
        dYaw = sx * md.x * rotateSpeed * dt;
        dPitch = sy * -md.y * pitchSpeed * dt;
    }

    float ReadZoomInput()
    {
        float scroll = GetScrollDelta().y;
        return scroll; // processed later with zoomSpeed
    }

    void EnforceGroundClearance()
    {
        // Keep camera at least minHeightAboveGround over ground; adjust pivot upward if needed
        Vector3 camPos = transform.position;
        if (Physics.Raycast(new Ray(camPos + Vector3.up * 0.5f, Vector3.down), out RaycastHit hit, 1000f, groundMask, QueryTriggerInteraction.Ignore))
        {
            float clearance = camPos.y - hit.point.y;
            if (clearance < minHeightAboveGround)
            {
                float pushUp = minHeightAboveGround - clearance;
                _pivot.position += Vector3.up * pushUp;
                _targetPivotPos = _pivot.position;
            }
        }
    }

    bool TryScreenCenterGround(out Vector3 hitPoint)
    {
        Vector3 center = new Vector3(Screen.width * 0.5f, Screen.height * 0.5f, 0f);
        Ray ray = _cam.ScreenPointToRay(center);
        if (Physics.Raycast(ray, out RaycastHit hit, 5000f, groundMask, QueryTriggerInteraction.Ignore))
        {
            hitPoint = hit.point;
            return true;
        }
        hitPoint = default;
        return false;
    }

    // ---------- Input Abstraction (Old and New Systems) ----------
    bool IsKeyHeld(KeyCode key)
    {
#if ENABLE_INPUT_SYSTEM
        // Input System does not have KeyCode directly for all cases; map common ones
        // Use Keyboard.current fallback
        var kb = Keyboard.current;
        if (kb == null) return false;
        switch (key)
        {
            case KeyCode.W: return kb.wKey.isPressed;
            case KeyCode.A: return kb.aKey.isPressed;
            case KeyCode.S: return kb.sKey.isPressed;
            case KeyCode.D: return kb.dKey.isPressed;
            case KeyCode.LeftShift: return kb.leftShiftKey.isPressed;
            case KeyCode.RightShift: return kb.rightShiftKey.isPressed;
            case KeyCode.UpArrow: return kb.upArrowKey.isPressed;
            case KeyCode.DownArrow: return kb.downArrowKey.isPressed;
            case KeyCode.LeftArrow: return kb.leftArrowKey.isPressed;
            case KeyCode.RightArrow: return kb.rightArrowKey.isPressed;
            default:
                // Fallback to legacy Input for uncommon keys if available
                return Input.GetKey(key);
        }
#else
        return Input.GetKey(key);
#endif
    }

    bool IsRMBHeld()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null && Mouse.current.rightButton.isPressed;
#else
        return Input.GetMouseButton(1);
#endif
    }
    bool IsMMBHeld()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null && Mouse.current.middleButton.isPressed;
#else
        return Input.GetMouseButton(2);
#endif
    }

    Vector2 GetMouseDelta()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null ? Mouse.current.delta.ReadValue() : Vector2.zero;
#else
        return new Vector2(Input.GetAxisRaw("Mouse X"), Input.GetAxisRaw("Mouse Y"));
#endif
    }

    Vector2 GetScrollDelta()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null ? Mouse.current.scroll.ReadValue() : Vector2.zero;
#else
        return new Vector2(0f, Input.mouseScrollDelta.y);
#endif
    }

    Vector2 GetMousePosition()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null ? Mouse.current.position.ReadValue() : Vector2.negativeInfinity;
#else
        return Input.mousePosition;
#endif
    }
}

