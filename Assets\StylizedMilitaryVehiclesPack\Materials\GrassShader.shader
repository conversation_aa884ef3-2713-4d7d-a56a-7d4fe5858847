﻿Shader "Custom/GrassShader" {
	Properties {
		_Color ("Color", Color) = (1,1,1,1)
		_MainTex ("Albedo (RGB)", 2D) = "white" {}
		_Glossiness ("Smoothness", Range(0,1)) = 0.5
		_Metallic ("Metallic", Range(0,1)) = 0.0
		_Speed("Sway Speed", Range(20,500)) = 25
		_Rigidness("Rigidness", Range(1,500)) = 25
		_SwayMax("Sway Max", Range(0, 0.1)) = 0.05
		_YOffset("Y Offset", float) = 0.5
	}
	SubShader {
		Tags { "RenderType"="Opaque" "DisableBatching" = "True" }
		LOD 200
		Cull Off

		CGPROGRAM
		// Physically based Standard lighting model, and enable shadows on all light types
		#pragma surface surf Standard fullforwardshadows vertex:vert addshadow 

		// Use shader model 3.0 target, to get nicer looking lighting
		#pragma target 3.0

		sampler2D _MainTex;

		struct Input {
			float2 uv_MainTex;
			fixed facing : VFACE;
		};

		half _Glossiness;
		half _Metallic;
		half _Speed;
		half _Rigidness;
		half _SwayMax;
		half _YOffset;
		fixed4 _Color;

		// Add instancing support for this shader. You need to check 'Enable Instancing' on materials that use the shader.
		// See https://docs.unity3d.com/Manual/GPUInstancing.html for more information about instancing.
		// #pragma instancing_options assumeuniformscaling
		UNITY_INSTANCING_BUFFER_START(Props)
			// put more per-instance properties here
		UNITY_INSTANCING_BUFFER_END(Props)

		void vert(inout appdata_full v) {
			float3 wpos = mul(unity_ObjectToWorld, v.vertex).xyz;
			float x = sin(wpos.x / _Rigidness + (_Time.x * _Speed)) * (v.vertex.y - _YOffset) * 5;
			float z = sin(wpos.z / _Rigidness + (_Time.x * _Speed)) * (v.vertex.y - _YOffset) * 5;

			v.vertex.x += step(0, v.vertex.y - _YOffset) * x * _SwayMax;
			v.vertex.z += step(0, v.vertex.y - _YOffset) * z * _SwayMax;
		}

		void surf (Input IN, inout SurfaceOutputStandard o) {

			// Albedo comes from a texture tinted by color
			fixed4 c = tex2D (_MainTex, IN.uv_MainTex) * _Color;
			o.Albedo = c.rgb;
			// Metallic and smoothness come from slider variables
			o.Metallic = _Metallic;
			o.Smoothness = _Glossiness;
			o.Alpha = c.a;
			if (IN.facing < 0.5)
			{
					o.Normal *= -1.0;
			}
		}
		ENDCG
	}
	FallBack "Diffuse"
}
