fileFormatVersion: 2
guid: 9ae3fde9ed7a8164b851c2b7a951b787
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Armature
    100002: Cube
    100004: Cube.001
    100006: Cube.002
    100008: Cube.003
    100010: L0
    100012: L06
    100014: L0_b
    100016: L0_b_end
    100018: L1
    100020: L10
    100022: L1_b
    100024: L1_b_end
    100026: L2
    100028: L2_b
    100030: L2_b_end
    100032: L3
    100034: L3_b
    100036: L3_b_end
    100038: L4
    100040: L4_b
    100042: L4_b_end
    100044: L5
    100046: L5_b
    100048: L5_b_end
    100050: L6_b
    100052: L6_b_end
    100054: L7
    100056: L7_b
    100058: L7_b_end
    100060: L8
    100062: L9
    100064: R0
    100066: R0_b
    100068: R0_b_end
    100070: R1
    100072: R10
    100074: R1_b
    100076: R1_b_end
    100078: R2
    100080: R2_b
    100082: R2_b_end
    100084: R3
    100086: R3_b
    100088: R3_b_end
    100090: R4
    100092: R4_b
    100094: R4_b_end
    100096: R5
    100098: R5_b
    100100: R5_b_end
    100102: R6
    100104: R6_b
    100106: R6_b_end
    100108: R7
    100110: R7_b
    100112: R7_b_end
    100114: R8
    100116: R9
    100118: root
    100120: //RootNode
    100122: Body
    100124: Gun
    100126: Turret
    100128: Trucks
    400000: Armature
    400002: Cube
    400004: Cube.001
    400006: Cube.002
    400008: Cube.003
    400010: L0
    400012: L06
    400014: L0_b
    400016: L0_b_end
    400018: L1
    400020: L10
    400022: L1_b
    400024: L1_b_end
    400026: L2
    400028: L2_b
    400030: L2_b_end
    400032: L3
    400034: L3_b
    400036: L3_b_end
    400038: L4
    400040: L4_b
    400042: L4_b_end
    400044: L5
    400046: L5_b
    400048: L5_b_end
    400050: L6_b
    400052: L6_b_end
    400054: L7
    400056: L7_b
    400058: L7_b_end
    400060: L8
    400062: L9
    400064: R0
    400066: R0_b
    400068: R0_b_end
    400070: R1
    400072: R10
    400074: R1_b
    400076: R1_b_end
    400078: R2
    400080: R2_b
    400082: R2_b_end
    400084: R3
    400086: R3_b
    400088: R3_b_end
    400090: R4
    400092: R4_b
    400094: R4_b_end
    400096: R5
    400098: R5_b
    400100: R5_b_end
    400102: R6
    400104: R6_b
    400106: R6_b_end
    400108: R7
    400110: R7_b
    400112: R7_b_end
    400114: R8
    400116: R9
    400118: root
    400120: //RootNode
    400122: Body
    400124: Gun
    400126: Turret
    400128: Trucks
    2100000: Trucks
    2100002: Palette
    2300000: Cube
    2300002: Cube.001
    2300004: Cube.002
    2300006: L0
    2300008: L06
    2300010: L1
    2300012: L10
    2300014: L2
    2300016: L3
    2300018: L4
    2300020: L5
    2300022: L7
    2300024: L8
    2300026: L9
    2300028: R0
    2300030: R1
    2300032: R10
    2300034: R2
    2300036: R3
    2300038: R4
    2300040: R5
    2300042: R6
    2300044: R7
    2300046: R8
    2300048: R9
    2300050: Body
    2300052: Gun
    2300054: Turret
    3300000: Cube
    3300002: Cube.001
    3300004: Cube.002
    3300006: L0
    3300008: L06
    3300010: L1
    3300012: L10
    3300014: L2
    3300016: L3
    3300018: L4
    3300020: L5
    3300022: L7
    3300024: L8
    3300026: L9
    3300028: R0
    3300030: R1
    3300032: R10
    3300034: R2
    3300036: R3
    3300038: R4
    3300040: R5
    3300042: R6
    3300044: R7
    3300046: R8
    3300048: R9
    3300050: Body
    3300052: Gun
    3300054: Turret
    4300000: Cube.003
    4300002: R0
    4300004: R1
    4300006: R7
    4300008: R2
    4300010: R3
    4300012: R4
    4300014: R5
    4300016: R6
    4300018: R8
    4300020: R9
    4300022: R10
    4300024: L10
    4300026: L9
    4300028: L8
    4300030: L06
    4300032: L5
    4300034: L4
    4300036: L3
    4300038: L2
    4300040: L7
    4300042: L1
    4300044: L0
    4300046: Cube.002
    4300048: Cube.001
    4300050: Cube
    4300052: Gun
    4300054: Turret
    4300056: Body
    4300058: Trucks
    6400000: Body
    6400002: Gun
    6400004: L0
    6400006: L06
    6400008: L1
    6400010: L10
    6400012: L2
    6400014: L3
    6400016: L4
    6400018: L5
    6400020: L7
    6400022: L8
    6400024: L9
    6400026: R0
    6400028: R1
    6400030: R10
    6400032: R2
    6400034: R3
    6400036: R4
    6400038: R5
    6400040: R6
    6400042: R7
    6400044: R8
    6400046: R9
    6400048: Turret
    9500000: //RootNode
    13700000: Cube.003
    13700002: Trucks
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 122675
  packageName: Stylized Military Vehicles Pack
  packageVersion: 1.02
  assetPath: Assets/StylizedMilitaryVehiclesPack/Models/Vehicles/Tank5/Tank5.fbx
  uploadId: 338346
