fileFormatVersion: 2
guid: 9e5c5f1c7a78ed5429e4ba6e811b9903
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: BL
    100002: Body
    100004: BR
    100006: FL
    100008: FL1
    100010: FR
    100012: FR1
    100014: //RootNode
    400000: BL
    400002: Body
    400004: BR
    400006: FL
    400008: FL1
    400010: FR
    400012: FR1
    400014: //RootNode
    2100000: No Name
    2100002: Palette
    2300000: BL
    2300002: Body
    2300004: BR
    2300006: FL
    2300008: FL1
    2300010: FR
    2300012: FR1
    3300000: BL
    3300002: Body
    3300004: BR
    3300006: FL
    3300008: FL1
    3300010: FR
    3300012: FR1
    4300000: FR
    4300002: FR1
    4300004: BR
    4300006: BL
    4300008: FL1
    4300010: FL
    4300012: Body
    6400000: BL
    6400002: Body
    6400004: BR
    6400006: FL
    6400008: FL1
    6400010: FR
    6400012: FR1
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 122675
  packageName: Stylized Military Vehicles Pack
  packageVersion: 1.02
  assetPath: Assets/StylizedMilitaryVehiclesPack/Models/Vehicles/LandArmored2/LandArmored2.fbx
  uploadId: 338346
