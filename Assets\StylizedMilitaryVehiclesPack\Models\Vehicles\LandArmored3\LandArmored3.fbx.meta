fileFormatVersion: 2
guid: b99f16aa356bbb2488855e203a86b084
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: BL
    100002: Body
    100004: BR
    100006: FL
    100008: FR
    100010: Gun
    100012: //RootNode
    100014: Turret
    400000: BL
    400002: Body
    400004: BR
    400006: FL
    400008: FR
    400010: Gun
    400012: //RootNode
    400014: Turret
    2100000: Palette
    2300000: BL
    2300002: Body
    2300004: BR
    2300006: FL
    2300008: FR
    2300010: Gun
    2300012: Turret
    3300000: BL
    3300002: Body
    3300004: BR
    3300006: FL
    3300008: FR
    3300010: Gun
    3300012: Turret
    4300000: FR
    4300002: BR
    4300004: BL
    4300006: Gun
    4300008: FL
    4300010: Turret
    4300012: Body
    6400000: BL
    6400002: Body
    6400004: BR
    6400006: FL
    6400008: FR
    6400010: Gun
    6400012: Turret
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 122675
  packageName: Stylized Military Vehicles Pack
  packageVersion: 1.02
  assetPath: Assets/StylizedMilitaryVehiclesPack/Models/Vehicles/LandArmored3/LandArmored3.fbx
  uploadId: 338346
