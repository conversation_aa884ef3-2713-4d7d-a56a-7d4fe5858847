fileFormatVersion: 2
guid: 6fb01fe37768da64cb743ebf041241e9
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Armature
    100002: Body
    100004: Gun
    100006: L0
    100008: L0_b
    100010: L0_b_end
    100012: L1
    100014: L1_b
    100016: L1_b_end
    100018: L2
    100020: L2_b
    100022: L2_b_end
    100024: L3
    100026: L3_b
    100028: L3_b_end
    100030: L4
    100032: L4_b
    100034: L4_b_end
    100036: L5
    100038: L5_b
    100040: L5_b_end
    100042: L6
    100044: L6_b
    100046: L6_b_end
    100048: L7
    100050: L7_b
    100052: L7_b_end
    100054: R0
    100056: R0_b
    100058: R0_b_end
    100060: R1
    100062: R1_b
    100064: R1_b_end
    100066: R2
    100068: R2_b
    100070: R2_b_end
    100072: R3
    100074: R3_b
    100076: R3_b_end
    100078: R4
    100080: R4_b
    100082: R4_b_end
    100084: R5
    100086: R5_b
    100088: R5_b_end
    100090: R6
    100092: R6_b
    100094: R6_b_end
    100096: R7
    100098: R7_b
    100100: R7_b_end
    100102: root
    100104: //RootNode
    100106: Trucks
    100108: Turret
    400000: Armature
    400002: Body
    400004: Gun
    400006: L0
    400008: L0_b
    400010: L0_b_end
    400012: L1
    400014: L1_b
    400016: L1_b_end
    400018: L2
    400020: L2_b
    400022: L2_b_end
    400024: L3
    400026: L3_b
    400028: L3_b_end
    400030: L4
    400032: L4_b
    400034: L4_b_end
    400036: L5
    400038: L5_b
    400040: L5_b_end
    400042: L6
    400044: L6_b
    400046: L6_b_end
    400048: L7
    400050: L7_b
    400052: L7_b_end
    400054: R0
    400056: R0_b
    400058: R0_b_end
    400060: R1
    400062: R1_b
    400064: R1_b_end
    400066: R2
    400068: R2_b
    400070: R2_b_end
    400072: R3
    400074: R3_b
    400076: R3_b_end
    400078: R4
    400080: R4_b
    400082: R4_b_end
    400084: R5
    400086: R5_b
    400088: R5_b_end
    400090: R6
    400092: R6_b
    400094: R6_b_end
    400096: R7
    400098: R7_b
    400100: R7_b_end
    400102: root
    400104: //RootNode
    400106: Trucks
    400108: Turret
    2100000: Trucks
    2100002: No Name
    2100004: Palette
    2300000: Body
    2300002: Gun
    2300004: L0
    2300006: L1
    2300008: L2
    2300010: L3
    2300012: L4
    2300014: L5
    2300016: L6
    2300018: L7
    2300020: R0
    2300022: R1
    2300024: R2
    2300026: R3
    2300028: R4
    2300030: R5
    2300032: R6
    2300034: R7
    2300036: Turret
    3300000: Body
    3300002: Gun
    3300004: L0
    3300006: L1
    3300008: L2
    3300010: L3
    3300012: L4
    3300014: L5
    3300016: L6
    3300018: L7
    3300020: R0
    3300022: R1
    3300024: R2
    3300026: R3
    3300028: R4
    3300030: R5
    3300032: R6
    3300034: R7
    3300036: Turret
    4300000: Trucks
    4300002: R0
    4300004: R1
    4300006: R6
    4300008: R5
    4300010: R4
    4300012: R3
    4300014: R2
    4300016: R7
    4300018: L7
    4300020: L2
    4300022: L3
    4300024: L4
    4300026: L5
    4300028: L6
    4300030: L1
    4300032: L0
    4300034: Gun
    4300036: Turret
    4300038: Body
    6400000: Body
    6400002: Gun
    6400004: L0
    6400006: L1
    6400008: L2
    6400010: L3
    6400012: L4
    6400014: L5
    6400016: L6
    6400018: L7
    6400020: R0
    6400022: R1
    6400024: R2
    6400026: R3
    6400028: R4
    6400030: R5
    6400032: R6
    6400034: R7
    6400036: Turret
    9500000: //RootNode
    13700000: Trucks
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 122675
  packageName: Stylized Military Vehicles Pack
  packageVersion: 1.02
  assetPath: Assets/StylizedMilitaryVehiclesPack/Models/Vehicles/Tank2/Tank2.fbx
  uploadId: 338346
