fileFormatVersion: 2
guid: 484ff72b3b2b2d1499a0414e38d5e689
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Armature
    100002: Body
    100004: //RootNode
    100006: FLT1
    100008: FLT2
    100010: FLT3
    100012: FRT1
    100014: FRT2
    100016: FRT3
    100018: Gun
    100020: L0
    100022: L0_b
    100024: L0_b_end
    100026: L1
    100028: L1_b
    100030: L1_b_end
    100032: L2
    100034: L2_b
    100036: L2_b_end
    100038: L3
    100040: L3_b
    100042: L3_b_end
    100044: L4
    100046: L4_b
    100048: L4_b_end
    100050: L5
    100052: L5_b
    100054: L5_b_end
    100056: L6
    100058: L6_b
    100060: L6_b_end
    100062: L7
    100064: L7_b
    100066: L7_b_end
    100068: R0
    100070: R0_b
    100072: R0_b_end
    100074: R1
    100076: R1_b
    100078: R1_b_end
    100080: R2
    100082: R2_b
    100084: R2_b_end
    100086: R3
    100088: R3_b
    100090: R3_b_end
    100092: R4
    100094: R4_b
    100096: R4_b_end
    100098: R5
    100100: R5_b
    100102: R5_b_end
    100104: R6
    100106: R6_b
    100108: R6_b_end
    100110: R7
    100112: R7_b
    100114: R7_b_end
    100116: root
    100118: Trucks
    100120: Turret
    400000: Armature
    400002: Body
    400004: //RootNode
    400006: FLT1
    400008: FLT2
    400010: FLT3
    400012: FRT1
    400014: FRT2
    400016: FRT3
    400018: Gun
    400020: L0
    400022: L0_b
    400024: L0_b_end
    400026: L1
    400028: L1_b
    400030: L1_b_end
    400032: L2
    400034: L2_b
    400036: L2_b_end
    400038: L3
    400040: L3_b
    400042: L3_b_end
    400044: L4
    400046: L4_b
    400048: L4_b_end
    400050: L5
    400052: L5_b
    400054: L5_b_end
    400056: L6
    400058: L6_b
    400060: L6_b_end
    400062: L7
    400064: L7_b
    400066: L7_b_end
    400068: R0
    400070: R0_b
    400072: R0_b_end
    400074: R1
    400076: R1_b
    400078: R1_b_end
    400080: R2
    400082: R2_b
    400084: R2_b_end
    400086: R3
    400088: R3_b
    400090: R3_b_end
    400092: R4
    400094: R4_b
    400096: R4_b_end
    400098: R5
    400100: R5_b
    400102: R5_b_end
    400104: R6
    400106: R6_b
    400108: R6_b_end
    400110: R7
    400112: R7_b
    400114: R7_b_end
    400116: root
    400118: Trucks
    400120: Turret
    2100000: Trucks
    2100002: Palette
    2300000: Body
    2300002: FLT1
    2300004: FLT2
    2300006: FLT3
    2300008: FRT1
    2300010: FRT2
    2300012: FRT3
    2300014: Gun
    2300016: L0
    2300018: L1
    2300020: L2
    2300022: L3
    2300024: L4
    2300026: L5
    2300028: L6
    2300030: L7
    2300032: R0
    2300034: R1
    2300036: R2
    2300038: R3
    2300040: R4
    2300042: R5
    2300044: R6
    2300046: R7
    2300048: Turret
    3300000: Body
    3300002: FLT1
    3300004: FLT2
    3300006: FLT3
    3300008: FRT1
    3300010: FRT2
    3300012: FRT3
    3300014: Gun
    3300016: L0
    3300018: L1
    3300020: L2
    3300022: L3
    3300024: L4
    3300026: L5
    3300028: L6
    3300030: L7
    3300032: R0
    3300034: R1
    3300036: R2
    3300038: R3
    3300040: R4
    3300042: R5
    3300044: R6
    3300046: R7
    3300048: Turret
    4300000: Trucks
    4300002: FRT1
    4300004: FRT2
    4300006: FRT3
    4300008: R0
    4300010: R1
    4300012: R2
    4300014: R3
    4300016: R4
    4300018: R5
    4300020: R6
    4300022: R7
    4300024: FLT3
    4300026: FLT2
    4300028: FLT1
    4300030: L7
    4300032: L6
    4300034: L5
    4300036: L4
    4300038: L3
    4300040: L2
    4300042: L1
    4300044: L0
    4300046: Gun
    4300048: Turret
    4300050: Body
    6400000: Body
    6400002: FLT1
    6400004: FLT2
    6400006: FLT3
    6400008: FRT1
    6400010: FRT2
    6400012: FRT3
    6400014: Gun
    6400016: L0
    6400018: L1
    6400020: L2
    6400022: L3
    6400024: L4
    6400026: L5
    6400028: L6
    6400030: L7
    6400032: R0
    6400034: R1
    6400036: R2
    6400038: R3
    6400040: R4
    6400042: R5
    6400044: R6
    6400046: R7
    6400048: Turret
    9500000: //RootNode
    13700000: Trucks
    2186277476908879412: ImportLogs
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 122675
  packageName: Stylized Military Vehicles Pack
  packageVersion: 1.02
  assetPath: Assets/StylizedMilitaryVehiclesPack/Models/Vehicles/FireSupport5/FireSupport5.fbx
  uploadId: 338346
