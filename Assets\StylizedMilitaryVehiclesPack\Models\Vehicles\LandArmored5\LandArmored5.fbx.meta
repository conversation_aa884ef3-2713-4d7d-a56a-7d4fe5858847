fileFormatVersion: 2
guid: 8b9fd40e7e4dd0e4faf476478d744929
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: BL
    100002: BL1
    100004: Body
    100006: BR
    100008: BR1
    100010: FL
    100012: FL1
    100014: FR
    100016: FR1
    100018: Gun
    100020: //RootNode
    100022: Turret
    400000: BL
    400002: BL1
    400004: Body
    400006: BR
    400008: BR1
    400010: FL
    400012: FL1
    400014: FR
    400016: FR1
    400018: Gun
    400020: //RootNode
    400022: Turret
    2100000: Palette
    2300000: BL
    2300002: BL1
    2300004: Body
    2300006: BR
    2300008: BR1
    2300010: FL
    2300012: FL1
    2300014: FR
    2300016: FR1
    2300018: Gun
    2300020: Turret
    3300000: BL
    3300002: BL1
    3300004: Body
    3300006: BR
    3300008: BR1
    3300010: FL
    3300012: FL1
    3300014: FR
    3300016: FR1
    3300018: Gun
    3300020: Turret
    4300000: FR
    4300002: FR1
    4300004: BR1
    4300006: BR
    4300008: BL
    4300010: BL1
    4300012: FL1
    4300014: Gun
    4300016: Turret
    4300018: FL
    4300020: Body
    6400000: BL
    6400002: BL1
    6400004: Body
    6400006: BR
    6400008: BR1
    6400010: FL
    6400012: FL1
    6400014: FR
    6400016: FR1
    6400018: Gun
    6400020: Turret
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 122675
  packageName: Stylized Military Vehicles Pack
  packageVersion: 1.02
  assetPath: Assets/StylizedMilitaryVehiclesPack/Models/Vehicles/LandArmored5/LandArmored5.fbx
  uploadId: 338346
