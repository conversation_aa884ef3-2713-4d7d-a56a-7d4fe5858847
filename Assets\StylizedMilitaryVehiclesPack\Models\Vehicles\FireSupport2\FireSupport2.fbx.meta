fileFormatVersion: 2
guid: e578d63dbaebdee4bafeb3544540f0f8
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Armature
    100002: BezierCurve
    100004: BL1
    100006: BL1_b
    100008: BL1_b_end
    100010: BL2
    100012: BL2_b
    100014: BL2_b_end
    100016: BL3
    100018: BL3_b
    100020: BL3_b_end
    100022: BL4
    100024: BL4_b
    100026: BL4_b_end
    100028: Body
    100030: BR1
    100032: BR1_b
    100034: BR1_b_end
    100036: BR2
    100038: BR2_b
    100040: BR2_b_end
    100042: BR3
    100044: BR3_b
    100046: BR3_b_end
    100048: BR4
    100050: BR4_b
    100052: BR4_b_end
    100054: //RootNode
    100056: FL0
    100058: FL0_b
    100060: FL0_b_end
    100062: FL1
    100064: FL1_b
    100066: FL1_b_end
    100068: FL2
    100070: FL2_b
    100072: FL2_b_end
    100074: FL3
    100076: FL3_b
    100078: FL3_b_end
    100080: FR0
    100082: FR0_b
    100084: FR0_b_end
    100086: FR1
    100088: FR1_b
    100090: FR1_b_end
    100092: FR2
    100094: FR2_b
    100096: FR2_b_end
    100098: FR3
    100100: FR3_b
    100102: FR3_b_end
    100104: Gun
    100106: root
    100108: root_end
    100110: Trucks
    100112: Turret
    400000: Armature
    400002: BezierCurve
    400004: BL1
    400006: BL1_b
    400008: BL1_b_end
    400010: BL2
    400012: BL2_b
    400014: BL2_b_end
    400016: BL3
    400018: BL3_b
    400020: BL3_b_end
    400022: BL4
    400024: BL4_b
    400026: BL4_b_end
    400028: Body
    400030: BR1
    400032: BR1_b
    400034: BR1_b_end
    400036: BR2
    400038: BR2_b
    400040: BR2_b_end
    400042: BR3
    400044: BR3_b
    400046: BR3_b_end
    400048: BR4
    400050: BR4_b
    400052: BR4_b_end
    400054: //RootNode
    400056: FL0
    400058: FL0_b
    400060: FL0_b_end
    400062: FL1
    400064: FL1_b
    400066: FL1_b_end
    400068: FL2
    400070: FL2_b
    400072: FL2_b_end
    400074: FL3
    400076: FL3_b
    400078: FL3_b_end
    400080: FR0
    400082: FR0_b
    400084: FR0_b_end
    400086: FR1
    400088: FR1_b
    400090: FR1_b_end
    400092: FR2
    400094: FR2_b
    400096: FR2_b_end
    400098: FR3
    400100: FR3_b
    400102: FR3_b_end
    400104: Gun
    400106: root
    400108: root_end
    400110: Trucks
    400112: Turret
    2100000: Trucks
    2100002: No Name
    2100004: Palette
    2300000: BezierCurve
    2300002: BL1
    2300004: BL2
    2300006: BL3
    2300008: BL4
    2300010: Body
    2300012: BR1
    2300014: BR2
    2300016: BR3
    2300018: BR4
    2300020: FL0
    2300022: FL1
    2300024: FL2
    2300026: FL3
    2300028: FR0
    2300030: FR1
    2300032: FR2
    2300034: FR3
    2300036: Gun
    2300038: Turret
    3300000: BezierCurve
    3300002: BL1
    3300004: BL2
    3300006: BL3
    3300008: BL4
    3300010: Body
    3300012: BR1
    3300014: BR2
    3300016: BR3
    3300018: BR4
    3300020: FL0
    3300022: FL1
    3300024: FL2
    3300026: FL3
    3300028: FR0
    3300030: FR1
    3300032: FR2
    3300034: FR3
    3300036: Gun
    3300038: Turret
    4300000: Trucks
    4300002: FR1
    4300004: FR2
    4300006: FR3
    4300008: BR1
    4300010: BR2
    4300012: BR3
    4300014: FR0
    4300016: BR4
    4300018: BL4
    4300020: FL0
    4300022: BL3
    4300024: BL2
    4300026: BL1
    4300028: FL3
    4300030: FL2
    4300032: FL1
    4300034: BezierCurve
    4300036: Gun
    4300038: Turret
    4300040: Body
    6400000: BezierCurve
    6400002: BL1
    6400004: BL2
    6400006: BL3
    6400008: BL4
    6400010: Body
    6400012: BR1
    6400014: BR2
    6400016: BR3
    6400018: BR4
    6400020: FL0
    6400022: FL1
    6400024: FL2
    6400026: FL3
    6400028: FR0
    6400030: FR1
    6400032: FR2
    6400034: FR3
    6400036: Gun
    6400038: Turret
    9500000: //RootNode
    13700000: Trucks
    2186277476908879412: ImportLogs
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 122675
  packageName: Stylized Military Vehicles Pack
  packageVersion: 1.02
  assetPath: Assets/StylizedMilitaryVehiclesPack/Models/Vehicles/FireSupport2/FireSupport2.fbx
  uploadId: 338346
