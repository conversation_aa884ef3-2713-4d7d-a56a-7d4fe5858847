fileFormatVersion: 2
guid: bbe547af72688804daf01f8fb5a98388
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Body
    100002: FL1
    100004: FL2
    100006: FL3
    100008: FR1
    100010: FR2
    100012: FR3
    100014: //RootNode
    400000: Body
    400002: FL1
    400004: FL2
    400006: FL3
    400008: FR1
    400010: FR2
    400012: FR3
    400014: //RootNode
    2100000: No Name
    2100002: Palette
    2300000: Body
    2300002: FL1
    2300004: FL2
    2300006: FL3
    2300008: FR1
    2300010: FR2
    2300012: FR3
    3300000: Body
    3300002: FL1
    3300004: FL2
    3300006: FL3
    3300008: FR1
    3300010: FR2
    3300012: FR3
    4300000: FR1
    4300002: FR2
    4300004: FR3
    4300006: FL3
    4300008: FL2
    4300010: FL1
    4300012: Body
    6400000: Body
    6400002: FL1
    6400004: FL2
    6400006: FL3
    6400008: FR1
    6400010: FR2
    6400012: FR3
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 122675
  packageName: Stylized Military Vehicles Pack
  packageVersion: 1.02
  assetPath: Assets/StylizedMilitaryVehiclesPack/Models/Vehicles/MilitaryTruck1/MilitaryTruck1.fbx
  uploadId: 338346
