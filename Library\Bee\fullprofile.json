{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 33816, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 33816, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 33816, "tid": 6601, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 33816, "tid": 6601, "ts": 1755987359255604, "dur": 494, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 33816, "tid": 6601, "ts": 1755987359259281, "dur": 660, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 33816, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 33816, "tid": 1, "ts": 1755987359069669, "dur": 3778, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 33816, "tid": 1, "ts": 1755987359073450, "dur": 52900, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 33816, "tid": 1, "ts": 1755987359126358, "dur": 24820, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 33816, "tid": 6601, "ts": 1755987359259944, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 33816, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359068164, "dur": 5777, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359073943, "dur": 174819, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359074873, "dur": 2319, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359077196, "dur": 1420, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359078620, "dur": 167, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359078790, "dur": 12, "ph": "X", "name": "ProcessMessages 20508", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359078803, "dur": 37, "ph": "X", "name": "ReadAsync 20508", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359078844, "dur": 26, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359078873, "dur": 23, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359078898, "dur": 26, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359078927, "dur": 22, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359078951, "dur": 25, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359078979, "dur": 21, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079003, "dur": 69, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079074, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079100, "dur": 22, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079124, "dur": 27, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079153, "dur": 2, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079155, "dur": 22, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079179, "dur": 26, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079208, "dur": 20, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079230, "dur": 18, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079251, "dur": 20, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079272, "dur": 21, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079295, "dur": 20, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079317, "dur": 19, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079339, "dur": 19, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079360, "dur": 20, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079382, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079405, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079428, "dur": 19, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079450, "dur": 19, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079471, "dur": 21, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079494, "dur": 19, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079515, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079537, "dur": 19, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079558, "dur": 28, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079588, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079612, "dur": 19, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079633, "dur": 20, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079655, "dur": 21, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079679, "dur": 20, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079701, "dur": 18, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079722, "dur": 23, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079747, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079770, "dur": 18, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079791, "dur": 20, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079813, "dur": 20, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079835, "dur": 19, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079856, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079875, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079898, "dur": 18, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079918, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079940, "dur": 19, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079962, "dur": 21, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359079985, "dur": 19, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080007, "dur": 20, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080030, "dur": 19, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080051, "dur": 18, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080071, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080094, "dur": 22, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080118, "dur": 20, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080140, "dur": 19, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080162, "dur": 21, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080186, "dur": 17, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080205, "dur": 20, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080226, "dur": 19, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080248, "dur": 19, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080269, "dur": 19, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080290, "dur": 18, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080310, "dur": 18, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080331, "dur": 18, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080351, "dur": 20, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080373, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080394, "dur": 19, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080415, "dur": 20, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080438, "dur": 20, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080459, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080461, "dur": 18, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080481, "dur": 37, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080521, "dur": 31, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080554, "dur": 25, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080581, "dur": 26, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080610, "dur": 21, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080633, "dur": 18, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080652, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080682, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080718, "dur": 24, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080744, "dur": 24, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080771, "dur": 21, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080794, "dur": 24, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080821, "dur": 33, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080856, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080879, "dur": 19, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080900, "dur": 21, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080924, "dur": 20, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080947, "dur": 21, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080971, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359080995, "dur": 16, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081014, "dur": 161, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081177, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081204, "dur": 31, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081238, "dur": 20, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081260, "dur": 20, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081282, "dur": 17, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081301, "dur": 18, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081321, "dur": 18, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081342, "dur": 26, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081370, "dur": 18, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081390, "dur": 19, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081412, "dur": 18, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081432, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081455, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081484, "dur": 22, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081508, "dur": 22, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081532, "dur": 19, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081553, "dur": 18, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081573, "dur": 17, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081593, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081627, "dur": 27, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081656, "dur": 20, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081678, "dur": 19, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081699, "dur": 20, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081720, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081746, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081775, "dur": 19, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081796, "dur": 19, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081817, "dur": 18, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081837, "dur": 18, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081858, "dur": 18, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081877, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081898, "dur": 20, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081920, "dur": 21, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081944, "dur": 17, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081963, "dur": 18, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359081983, "dur": 17, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082001, "dur": 18, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082023, "dur": 23, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082049, "dur": 25, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082076, "dur": 19, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082099, "dur": 26, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082127, "dur": 19, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082148, "dur": 19, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082169, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082190, "dur": 19, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082211, "dur": 19, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082232, "dur": 17, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082251, "dur": 19, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082271, "dur": 16, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082290, "dur": 17, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082309, "dur": 19, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082330, "dur": 19, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082351, "dur": 19, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082373, "dur": 19, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082394, "dur": 17, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082413, "dur": 19, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082434, "dur": 17, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082454, "dur": 18, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082474, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082495, "dur": 18, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082515, "dur": 17, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082535, "dur": 18, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082555, "dur": 19, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082576, "dur": 19, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082597, "dur": 18, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082618, "dur": 20, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082641, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082665, "dur": 21, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082689, "dur": 19, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082710, "dur": 18, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082730, "dur": 18, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082751, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082771, "dur": 19, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082791, "dur": 20, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082813, "dur": 19, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082835, "dur": 18, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082855, "dur": 18, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082875, "dur": 17, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082894, "dur": 18, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082914, "dur": 19, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082935, "dur": 18, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082955, "dur": 18, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082975, "dur": 20, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359082997, "dur": 18, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083017, "dur": 17, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083036, "dur": 19, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083057, "dur": 18, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083077, "dur": 19, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083098, "dur": 18, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083118, "dur": 19, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083141, "dur": 23, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083166, "dur": 23, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083192, "dur": 18, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083212, "dur": 20, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083235, "dur": 19, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083256, "dur": 18, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083277, "dur": 19, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083298, "dur": 23, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083324, "dur": 18, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083344, "dur": 20, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083366, "dur": 20, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083388, "dur": 19, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083410, "dur": 18, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083430, "dur": 20, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083452, "dur": 18, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083472, "dur": 18, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083492, "dur": 18, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083512, "dur": 19, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083534, "dur": 18, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083554, "dur": 18, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083575, "dur": 21, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083597, "dur": 18, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083618, "dur": 19, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083639, "dur": 30, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083672, "dur": 19, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083693, "dur": 19, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083714, "dur": 19, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083735, "dur": 19, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083756, "dur": 18, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083776, "dur": 19, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083797, "dur": 18, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083818, "dur": 17, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083836, "dur": 18, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083856, "dur": 18, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083876, "dur": 20, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083899, "dur": 19, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083920, "dur": 19, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083941, "dur": 18, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083961, "dur": 18, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359083981, "dur": 17, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084000, "dur": 17, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084019, "dur": 19, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084040, "dur": 19, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084061, "dur": 18, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084081, "dur": 19, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084102, "dur": 18, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084122, "dur": 20, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084145, "dur": 18, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084164, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084166, "dur": 19, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084187, "dur": 18, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084208, "dur": 17, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084228, "dur": 20, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084250, "dur": 19, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084271, "dur": 21, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084294, "dur": 20, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084317, "dur": 19, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084338, "dur": 19, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084359, "dur": 19, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084381, "dur": 19, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084402, "dur": 18, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084422, "dur": 19, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084444, "dur": 18, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084464, "dur": 18, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084484, "dur": 18, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084504, "dur": 20, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084525, "dur": 2, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084528, "dur": 81, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084611, "dur": 63, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084681, "dur": 3, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084685, "dur": 202, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084891, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084893, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359084959, "dur": 264, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085228, "dur": 79, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085309, "dur": 6, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085317, "dur": 57, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085379, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085383, "dur": 69, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085455, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085458, "dur": 41, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085502, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085505, "dur": 38, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085547, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085549, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085613, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085617, "dur": 62, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085682, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085686, "dur": 66, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085757, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085762, "dur": 65, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085830, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085834, "dur": 57, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085895, "dur": 3, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085899, "dur": 54, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085956, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359085959, "dur": 45, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086007, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086009, "dur": 45, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086058, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086064, "dur": 50, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086117, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086121, "dur": 44, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086169, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086172, "dur": 43, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086217, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086219, "dur": 46, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086268, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086271, "dur": 52, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086326, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086329, "dur": 37, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086369, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086370, "dur": 39, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086412, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086415, "dur": 42, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086461, "dur": 1, "ph": "X", "name": "ProcessMessages 95", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086480, "dur": 49, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086532, "dur": 146, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086680, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086740, "dur": 4, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359086745, "dur": 765, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359087513, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359087516, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359087558, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359087560, "dur": 8173, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359095736, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359095739, "dur": 255, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359096147, "dur": 142, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359096502, "dur": 547, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359097123, "dur": 38, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359097322, "dur": 846, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359098266, "dur": 57, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359098411, "dur": 443, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359098904, "dur": 87, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359099099, "dur": 263, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359099437, "dur": 3, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359099696, "dur": 324, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359100070, "dur": 35, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359100143, "dur": 239, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359100454, "dur": 42, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359100531, "dur": 38, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359100632, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359100669, "dur": 268, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359100969, "dur": 61, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359101068, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359101313, "dur": 464, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359101931, "dur": 30, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359101993, "dur": 401, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359102445, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359102483, "dur": 638, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359103130, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359103132, "dur": 741, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359104099, "dur": 33, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359104202, "dur": 332, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359104545, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359104547, "dur": 427, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359105005, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359105043, "dur": 129, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359105206, "dur": 540, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359105822, "dur": 115837, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359221667, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359221671, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359221713, "dur": 1234, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359222949, "dur": 3062, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359226014, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359226018, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359226047, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359226049, "dur": 1840, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359227893, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359227919, "dur": 8, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359227928, "dur": 11902, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359239835, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359239870, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359239872, "dur": 947, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359240823, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359240898, "dur": 22, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359240921, "dur": 741, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359241666, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359241700, "dur": 480, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755987359242182, "dur": 5952, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 33816, "tid": 6601, "ts": 1755987359259954, "dur": 445, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 33816, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 33816, "tid": 8589934592, "ts": 1755987359065609, "dur": 85627, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 33816, "tid": 8589934592, "ts": 1755987359151238, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 33816, "tid": 8589934592, "ts": 1755987359151243, "dur": 1055, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 33816, "tid": 6601, "ts": 1755987359260401, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 33816, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 33816, "tid": 4294967296, "ts": 1755987359049691, "dur": 199902, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 33816, "tid": 4294967296, "ts": 1755987359053414, "dur": 7094, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 33816, "tid": 4294967296, "ts": 1755987359249734, "dur": 3538, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 33816, "tid": 4294967296, "ts": 1755987359251692, "dur": 79, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 33816, "tid": 4294967296, "ts": 1755987359253341, "dur": 14, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 33816, "tid": 6601, "ts": 1755987359260407, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1755987359071432, "dur": 1537, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755987359072978, "dur": 351, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755987359073409, "dur": 660, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755987359075099, "dur": 1159, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_B0863ADC38A40DC9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755987359077248, "dur": 1290, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1755987359074093, "dur": 10244, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755987359084344, "dur": 156310, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755987359240656, "dur": 154, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755987359240811, "dur": 242, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755987359241053, "dur": 61, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755987359241399, "dur": 1082, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1755987359073782, "dur": 10570, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359084365, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E4EBE1DEA4B9DBF9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755987359084542, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359084741, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_574DE5121CC9D879.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755987359084810, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359084870, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_88DF725418823539.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755987359084931, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359084990, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_88DF725418823539.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755987359085090, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_AF06999FBA4544CA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755987359085146, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359085332, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_AF06999FBA4544CA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755987359085386, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_0A94E029953135AC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755987359085494, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_C2B2B5EFE7050494.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755987359085585, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_C2B2B5EFE7050494.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755987359085667, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359086047, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1755987359086229, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1755987359086405, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359087559, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359088506, "dur": 1391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359089898, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359091057, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359092427, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359093317, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359094171, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359094530, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359095414, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359095986, "dur": 808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359096794, "dur": 750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359097544, "dur": 723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359098267, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359098490, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359098564, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359098717, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359098820, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359099290, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359099374, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359099625, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359099964, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359100026, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359100078, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359100523, "dur": 884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359101407, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359101616, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359102066, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359102362, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359102883, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755987359103425, "dur": 137256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359073832, "dur": 10542, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359084383, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0903FEE7BAED8E66.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755987359084704, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359084822, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_40502E6DCAE34EBD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755987359084903, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359085003, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_886CDD09B49D7AF9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755987359085086, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359085144, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_886CDD09B49D7AF9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755987359085204, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E38ED73995C25B9E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755987359085397, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E38ED73995C25B9E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755987359085595, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755987359085683, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755987359085800, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359085904, "dur": 473, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1755987359086424, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1755987359086560, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359087703, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359088756, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359090006, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359090933, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359092119, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359093028, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359093871, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359094742, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359095592, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359095995, "dur": 814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359096809, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359097399, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755987359097614, "dur": 913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755987359098527, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359098828, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755987359098974, "dur": 796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755987359099770, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359100066, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359100547, "dur": 891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359101439, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359101593, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359102094, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359102321, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359102838, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755987359103420, "dur": 137250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359073820, "dur": 10546, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359084372, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B3B4C94707F7A218.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755987359084673, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359084740, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_47278636E8915295.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755987359084799, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359084864, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_21F715593E655655.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755987359084921, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359085023, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359085168, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359085309, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_3BECFE23642E1E2A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755987359085431, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F129D49E2ADECCC2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755987359085497, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359085553, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F129D49E2ADECCC2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755987359085788, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755987359085977, "dur": 9879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755987359095857, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359096047, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755987359096154, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755987359096671, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359096848, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755987359096942, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755987359097209, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359097387, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755987359097595, "dur": 1462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755987359099057, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359099224, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755987359099426, "dur": 973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755987359100400, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359100572, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755987359100668, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755987359101295, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359101461, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755987359101592, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755987359101919, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359102133, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359102294, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359102852, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755987359103409, "dur": 137263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359074214, "dur": 10373, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359084600, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_20112DFB1B9A6681.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755987359084690, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359084803, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_7D055410D6C309E4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755987359084865, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359084945, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_DA76F58CBF310F62.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755987359085065, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_A067B7A9CA1EAF47.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755987359085197, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_3FD29ACDA5717940.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755987359085612, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359085678, "dur": 490, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_EF1B9BDC36CA0637.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755987359086176, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755987359086325, "dur": 8847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755987359095173, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359095339, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359095399, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359096011, "dur": 803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359096814, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359097385, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755987359097676, "dur": 738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755987359098414, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359098587, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755987359098780, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755987359099246, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359099554, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359100102, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359100532, "dur": 885, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359101417, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359101585, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359102084, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359102306, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359102870, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755987359103398, "dur": 137254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359073892, "dur": 10511, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359084410, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_EA0B5218CB807CE8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755987359084715, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359084791, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_2731044146C71882.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755987359084906, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359084993, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6941CE050E1CF5A2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755987359085078, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359085148, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_88D8DDE2BF366D61.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755987359085300, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359085360, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_0B128FF1BBAAC47E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755987359085459, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_0B128FF1BBAAC47E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755987359085729, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755987359085991, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755987359086101, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755987359086423, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359087529, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359088484, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359089639, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359090695, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359091612, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359092800, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359093623, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359094490, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359095379, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359095978, "dur": 798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359096777, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359097331, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359097391, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755987359097538, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359097610, "dur": 772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755987359098383, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359098547, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359098608, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755987359098788, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755987359099371, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359099602, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359099972, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359100094, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359100558, "dur": 845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359101403, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359101586, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359102085, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359102309, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359102831, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755987359103395, "dur": 137255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359073919, "dur": 10497, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359084425, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_B16CC6C88F056D62.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755987359084752, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359084935, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359084989, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_FC8457FE0E2359F5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755987359085128, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D8EA2BB5196147D6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755987359085300, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359085389, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_4CE88DA2C6FA0DDC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755987359085473, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359085530, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_4CE88DA2C6FA0DDC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755987359085596, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_3D53C0654B9396F1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755987359085846, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755987359086051, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1755987359086252, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755987359086388, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755987359086513, "dur": 1580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359088094, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359089375, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359090443, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359091337, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359092563, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359093412, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359094326, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359095170, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359095331, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359095420, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359096012, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359096798, "dur": 687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359097486, "dur": 764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359098251, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359098400, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359098561, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359098717, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359098795, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359099359, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359099560, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359099961, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359100109, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359100525, "dur": 884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359101409, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359101598, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359102097, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359102323, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359102837, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755987359103412, "dur": 137289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359074297, "dur": 10326, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359084633, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_94137FF513C48ABF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755987359084707, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359084832, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5706F0A31B0C16B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755987359085164, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_57A4CF732EF2CD29.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755987359085217, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_CBCEC9956E2477BD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755987359085533, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7E5B9059DDF5C58D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755987359085878, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1755987359086032, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359086274, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755987359086443, "dur": 1890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359088334, "dur": 1410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359089745, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359090765, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359091671, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359092849, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359093684, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359094571, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359095505, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359095998, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359096816, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359097506, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359098304, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359098412, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359098477, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359098574, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359098814, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359099240, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755987359099431, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755987359099849, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359100073, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359100518, "dur": 879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359101397, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359101572, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359102092, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359102314, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359102849, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755987359103436, "dur": 137271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359073982, "dur": 10493, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359084487, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_3F0948DC6B40CBC0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755987359084582, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359084695, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_3A57A969F158AF6F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755987359084791, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359084910, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_080CB5314CBF4724.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755987359085026, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_080CB5314CBF4724.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755987359085219, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_B2740E25E009BD1A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755987359085386, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359085585, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_81ADD57237B7A215.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755987359085642, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755987359085737, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359085946, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1755987359085998, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755987359086260, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755987359086386, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755987359086451, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359086511, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359087678, "dur": 1464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359089142, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359090452, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359091309, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359092597, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359093464, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359094387, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359095248, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359095401, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359095983, "dur": 805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359096788, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359097589, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359098259, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359098462, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359098530, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359098595, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359098786, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359099226, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755987359099406, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755987359099809, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359100137, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359100545, "dur": 876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359101422, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359101570, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359102063, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359102289, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359102360, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359102864, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755987359103416, "dur": 137260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359074022, "dur": 10466, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359084545, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359084625, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_DA0ED1A2C90566BA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755987359084724, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_324EC462D42B6AD5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755987359084831, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359084903, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_71130C2744ADE79E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755987359084955, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359085015, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_71130C2744ADE79E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755987359085118, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_AFDD633454108E81.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755987359085172, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359085365, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_71DE750A6D09CA7B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755987359085556, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1755987359085769, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359085841, "dur": 592, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1755987359086434, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359087567, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359088467, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359089805, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359090924, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359092097, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359093211, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359094088, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359094943, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359095761, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359096015, "dur": 775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359096791, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359097554, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359098270, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359098406, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359098610, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359098780, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359098943, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359099184, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359099243, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359099344, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359099598, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359099971, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359100110, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359100530, "dur": 880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359101411, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359101608, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359102101, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359102324, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359102846, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755987359103432, "dur": 137262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359074069, "dur": 10434, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359084515, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_12BCE5693E524B55.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755987359084657, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_C223B6F3143702D1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755987359084722, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359084827, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_3A6F5DE8BB04652D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755987359084892, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359084966, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B76ACB483990A7B3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755987359085078, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359085179, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6583F22C0A896B6F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755987359085384, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_448E988461C74DB0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755987359085436, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359085537, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359085600, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1755987359085738, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1755987359086014, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755987359086175, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1755987359086245, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755987359086697, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359086788, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359088086, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359089334, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359090367, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359091403, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359092664, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359093515, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359094497, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359095392, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359095969, "dur": 805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359096817, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359097565, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359098274, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359098405, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359098614, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359098722, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359098822, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359099241, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359099344, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359099408, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359099575, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359099960, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359100026, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359100260, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359100551, "dur": 878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359101429, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359101574, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359102070, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359102290, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359102812, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359102869, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755987359103404, "dur": 137259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359074110, "dur": 10409, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359084529, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_C25CFDCCBD0E71F0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755987359084721, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_4297D6ED2C7F7138.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755987359084834, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359084943, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_EC805490FEC13931.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755987359085001, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359085091, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_F9E2212BE69EFFE3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755987359085150, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359085300, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_6A4766F7572387DA.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755987359085414, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3352A694B6772687.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755987359085469, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359085535, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3352A694B6772687.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755987359085840, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1755987359085973, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1755987359086062, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1755987359086383, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1755987359086500, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359087579, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359088527, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359089764, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359090771, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359091611, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359092875, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359093723, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359094650, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359095522, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359095991, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359096803, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359097497, "dur": 750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359098252, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359098319, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359098460, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359098527, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359098579, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359098817, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359099240, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359099346, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359099545, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359099945, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359100028, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359100516, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359100577, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755987359100704, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755987359101446, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359101630, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755987359101748, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755987359102186, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359102357, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755987359102457, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755987359102759, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359102867, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755987359102968, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755987359103250, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755987359103478, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755987359103605, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755987359105336, "dur": 116058, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755987359225607, "dur": 13943, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1755987359225589, "dur": 13961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1755987359239565, "dur": 1001, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1755987359074153, "dur": 10379, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359084543, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_51DEFFE41CB8BCA4.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755987359084616, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359084715, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_C1731F7A9273BA75.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755987359084792, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359084856, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_F3E222EC1821C3F6.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755987359084966, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_F3E222EC1821C3F6.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755987359085051, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_6EEC5CB3D74D8DC0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755987359085192, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_F7EA645C15EDC022.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755987359085388, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359085541, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_F7EA645C15EDC022.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755987359085603, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359085657, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1755987359085822, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755987359085935, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755987359086073, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359086279, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755987359086435, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359087640, "dur": 989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359088630, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359089897, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359090811, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359091668, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359092936, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359093772, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359094913, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359095758, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359096012, "dur": 783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359096795, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359097332, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359097383, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755987359097546, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755987359098123, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359098315, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359098466, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359098542, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359098603, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359098780, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359099181, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359099244, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359099386, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359099605, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359099974, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359100084, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359100520, "dur": 877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359101398, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359101571, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359102061, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359102293, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359102825, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755987359103434, "dur": 137261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359074179, "dur": 10379, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359084571, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_6B03EF9A2B2BFBD3.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755987359084659, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359084732, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_5A472186080A5AB1.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755987359084964, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_E57DD11F6513DD1B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755987359085020, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359085175, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359085338, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_A964C83E710562E0.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755987359085571, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755987359085678, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755987359085890, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359085945, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1755987359086141, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1755987359086244, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359086428, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359087722, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359088824, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359090092, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359091054, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359092270, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359093180, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359094046, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359094921, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359095784, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359095973, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359096785, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359097335, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359097390, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755987359097634, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755987359098253, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359098544, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755987359098721, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755987359099150, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359099263, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359099421, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359099553, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359100031, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359100517, "dur": 882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359101400, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359101609, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359102106, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359102327, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359102845, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755987359103423, "dur": 137256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359073870, "dur": 10519, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359084400, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_B9DC0DB8A04DAC4C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755987359084782, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359084949, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_D2ABA6F656B34D47.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755987359085015, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359085065, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_D2ABA6F656B34D47.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755987359085181, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_43979216E7A92FE9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755987359085394, "dur": 274, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_43979216E7A92FE9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755987359085677, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1755987359085766, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359085868, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359086049, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1755987359086176, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1755987359086394, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1755987359086523, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359087656, "dur": 1516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359089172, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359090259, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359091215, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359092422, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359094232, "dur": 567, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@8f3c30ecd081\\Editor\\Views\\Changesets\\ChangesetExplorerView.cs"}}, {"pid": 12345, "tid": 14, "ts": 1755987359093312, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359094849, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359095660, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359096000, "dur": 815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359096815, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359097460, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755987359097688, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1755987359098263, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359098467, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359098574, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359098811, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359099242, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359099390, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359099584, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359099967, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359100119, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359100535, "dur": 883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359101418, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359101575, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359102074, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359102358, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755987359102464, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1755987359102711, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359102887, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755987359103407, "dur": 137276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359074237, "dur": 10364, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359084610, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C124AD4B97AA3982.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755987359084708, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359084796, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_80D3DD5FCF7A4155.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755987359084952, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_47FD15CC38401525.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755987359085070, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_3AD12250A59F4CEE.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755987359085124, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359085182, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_2CD26214566796B4.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755987359085367, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_36F2911304CC020F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755987359085657, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1755987359085905, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1755987359086118, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1755987359086291, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1755987359086382, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359086495, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359087771, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359088762, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359089925, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359090982, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359092227, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359093122, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359093950, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359094814, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359095633, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359095987, "dur": 819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359096806, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359097437, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755987359097673, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755987359098299, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359098568, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359098805, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359099265, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359099413, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359099564, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359099959, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359100027, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359100519, "dur": 876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359101431, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359101580, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359102077, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359102300, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359102842, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755987359103431, "dur": 137258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359074260, "dur": 10354, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359084623, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_1C4A8B1B6898FD4D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755987359084750, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_7CE7E2E85C9B872F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755987359084847, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359085011, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_5BF450534C33B93C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755987359085188, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_AF814272D3883F57.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755987359085392, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_AF814272D3883F57.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755987359085574, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359085629, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_B3E44AC0B8D941C7.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755987359085730, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_B3E44AC0B8D941C7.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755987359085949, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1755987359086081, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359086244, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1755987359086412, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359087638, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359088759, "dur": 1604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359090364, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359091300, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359092596, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359093469, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359094356, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359095226, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359095400, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359095980, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359096793, "dur": 741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359097535, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359098263, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359098444, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359098566, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359098749, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359098801, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359099289, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359099382, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359099617, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359099978, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359100065, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359100531, "dur": 882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359101414, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359101612, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359102108, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359102338, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359102851, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755987359103449, "dur": 137249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359073951, "dur": 10493, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359084458, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_943D1566EB36197C.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755987359084543, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359084644, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4C8A9319ED1CA3AE.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755987359084836, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D443F0926FAEB623.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755987359085163, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8065517B5A536D03.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755987359085223, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_47D5F37EEAD56BE0.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755987359085406, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359085534, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359085588, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1755987359085659, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359085713, "dur": 275, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1755987359085990, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1755987359086104, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1755987359086365, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1755987359086439, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359087685, "dur": 1746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359089432, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359090714, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359092096, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359093059, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359093931, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359094860, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359095595, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359095993, "dur": 814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359096808, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359097423, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755987359097674, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359097765, "dur": 1422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1755987359099188, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359099403, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755987359099640, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1755987359100111, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359100253, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359100549, "dur": 881, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359101430, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359101575, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359102075, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359102298, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359102829, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359103383, "dur": 48663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359153353, "dur": 224, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.1f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 17, "ts": 1755987359152047, "dur": 1535, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755987359153583, "dur": 87127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359074322, "dur": 10317, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359084651, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_446E57718A74AE8B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755987359084733, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359084976, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_B0863ADC38A40DC9.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755987359085113, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1CF8F8636ADD1698.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755987359085164, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359085336, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1CF8F8636ADD1698.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755987359085719, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_097DDEAFECA28810.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755987359085791, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1755987359085943, "dur": 278, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1755987359086297, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1755987359086450, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359087560, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359088484, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359089928, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359090979, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359092163, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359093044, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359093866, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359094753, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359095747, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359096010, "dur": 802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359096812, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359097448, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755987359097705, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1755987359098335, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359098529, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359098588, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359098818, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359099276, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359099410, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755987359099595, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1755987359099988, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359100113, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359100537, "dur": 889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359101426, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359101582, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359102080, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359102303, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359102859, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755987359103426, "dur": 137259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359074347, "dur": 10312, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359084672, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_D83BBE6D171D2E8B.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755987359084830, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359084901, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_B9618BB877B2AF9C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755987359084958, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359085025, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_B9618BB877B2AF9C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755987359085143, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F15DE0412BCD77BB.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755987359085308, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359085366, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F15DE0412BCD77BB.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755987359085453, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_BB73E5A042403DAF.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755987359085519, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359085591, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755987359085646, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359086044, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359086106, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1755987359086239, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1755987359086493, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359087922, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359088882, "dur": 1501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359090384, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359091235, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359092486, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359093359, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359094339, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359095254, "dur": 79, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359095377, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359095970, "dur": 805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359096824, "dur": 642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359097474, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755987359097723, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1755987359098534, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359098802, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359099279, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359099389, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359099594, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359099970, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359100128, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359100539, "dur": 889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359101429, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359101572, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359102110, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359102330, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359102860, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755987359103427, "dur": 137289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359074382, "dur": 10293, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359084684, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_EB9810B219936736.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1755987359084829, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359084928, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_EB9810B219936736.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1755987359085103, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_20E9E08310ABADB8.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1755987359085169, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359085363, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359085452, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_35E2FC16174B6AD9.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1755987359085560, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_18E07CEE712070C7.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1755987359085705, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1755987359085934, "dur": 318, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1755987359086253, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1755987359086416, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359087680, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359088657, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359089935, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359090991, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359092186, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359093068, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359093926, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359094845, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359095729, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359096004, "dur": 819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359096823, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359097361, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359097577, "dur": 698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359098275, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359098408, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359098491, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359098558, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359098716, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359098781, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359099180, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359099247, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359099436, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359099544, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359099964, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359100146, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359100557, "dur": 878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359101435, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359101603, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359102099, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359102318, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359102854, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755987359103439, "dur": 137249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359074409, "dur": 10282, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359084699, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_AF9E01432509B98A.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1755987359084812, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359084968, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_558CAE984AE1D1A5.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1755987359085036, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359085088, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_558CAE984AE1D1A5.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1755987359085224, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_9CCBC171CB8C01C8.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1755987359085387, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359085549, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_9CCBC171CB8C01C8.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1755987359085740, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1755987359085818, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1755987359085904, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359086149, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1755987359086473, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1755987359086541, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359087806, "dur": 1509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359089316, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359090351, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359091226, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359092497, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359093363, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359094209, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359095012, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359095729, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359096008, "dur": 792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359096800, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359097477, "dur": 775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359098253, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359098401, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359098469, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359098600, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359098798, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359099298, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359099366, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359099557, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359099966, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359100155, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359100556, "dur": 884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359101441, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359101586, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359102089, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359102319, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359102835, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755987359103402, "dur": 137246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359074430, "dur": 10273, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359084712, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_798AE90902F00157.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755987359084824, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359084923, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_D52594234CD03E41.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755987359085001, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359085108, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_128BE6A580A94DC6.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755987359085170, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359085323, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_076091C3DFC2A810.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755987359085397, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359085457, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B40F3C4651EACB98.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755987359085510, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359085581, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755987359085676, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755987359085809, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359085871, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359086046, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359086135, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359086185, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1755987359086289, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1755987359086446, "dur": 1959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359088406, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359089506, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359090589, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359091448, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359092676, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359093546, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359094411, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359095388, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359096006, "dur": 823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359096829, "dur": 687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359097516, "dur": 746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359098262, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359098459, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359098565, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359098715, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359098782, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359099233, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755987359099429, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1755987359099860, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359100056, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359100554, "dur": 880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359101434, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359101584, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359102081, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359102305, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359102824, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359103384, "dur": 50204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755987359153589, "dur": 87123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359074452, "dur": 10265, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359084727, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_D95410EB09159078.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755987359084803, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359084946, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_EFBDB924F7F1B25C.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755987359085018, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359085071, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_EFBDB924F7F1B25C.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755987359085247, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_9520DE7000879E41.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755987359085401, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359085455, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_9520DE7000879E41.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755987359085544, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359085839, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1755987359086000, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1755987359086059, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359086172, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1755987359086420, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359087540, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359088800, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359090089, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359091206, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359092394, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359093237, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359094111, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359094783, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359095635, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359096004, "dur": 779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359096783, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359097389, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755987359097659, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1755987359098260, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359098465, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359098535, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359098592, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359098792, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359099349, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359099548, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359100046, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359100527, "dur": 886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359101413, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359101596, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359102095, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359102316, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359102836, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755987359103406, "dur": 137260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359074474, "dur": 10269, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359084744, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_4F4FD9FF61AF6F28.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755987359084922, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_4F4FD9FF61AF6F28.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755987359084991, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_F270133E58124F18.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755987359085122, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_0548B6350D93892B.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755987359085173, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359085364, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_253848F44BDD5E17.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755987359085477, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359085533, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_253848F44BDD5E17.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755987359085773, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1755987359085954, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359086109, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1755987359086240, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1755987359086408, "dur": 1635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359088043, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359089316, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359090271, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359091144, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359092395, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359093285, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359094229, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359095085, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359095695, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359096007, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359096832, "dur": 693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359097525, "dur": 741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359098266, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359098394, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359098461, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359098573, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359098808, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359099255, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359099427, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359099585, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359099963, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359100062, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359100522, "dur": 874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359101442, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359101589, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359102091, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359102311, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359102857, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359103392, "dur": 122203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755987359225643, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 24, "ts": 1755987359225596, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 24, "ts": 1755987359225783, "dur": 1856, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 24, "ts": 1755987359227641, "dur": 13016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755987359245576, "dur": 1818, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 33816, "tid": 6601, "ts": 1755987359260739, "dur": 1774, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 33816, "tid": 6601, "ts": 1755987359262563, "dur": 1677, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 33816, "tid": 6601, "ts": 1755987359258059, "dur": 6958, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}