fileFormatVersion: 2
guid: 15d025ff347e38142b709933ee86f9f4
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: BL
    100002: BR
    100004: //RootNode
    100006: CarBody
    100008: CarGun
    100010: FL
    100012: FR
    100014: Rotator
    100016: rudder
    400000: BL
    400002: BR
    400004: //RootNode
    400006: CarBody
    400008: CarGun
    400010: FL
    400012: FR
    400014: Rotator
    400016: rudder
    2100000: Palette
    2100002: No Name
    2300000: BL
    2300002: BR
    2300004: CarBody
    2300006: CarGun
    2300008: FL
    2300010: FR
    2300012: Rotator
    2300014: rudder
    3300000: BL
    3300002: BR
    3300004: CarBody
    3300006: CarGun
    3300008: FL
    3300010: FR
    3300012: Rotator
    3300014: rudder
    4300000: Rotator
    4300002: CarGun
    4300004: rudder
    4300006: BR
    4300008: FR
    4300010: BL
    4300012: FL
    4300014: CarBody
    6400000: BL
    6400002: BR
    6400004: CarBody
    6400006: CarGun
    6400008: FL
    6400010: FR
    6400012: Rotator
    6400014: rudder
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 122675
  packageName: Stylized Military Vehicles Pack
  packageVersion: 1.02
  assetPath: Assets/StylizedMilitaryVehiclesPack/Models/Vehicles/Car4/Car4.fbx
  uploadId: 338346
