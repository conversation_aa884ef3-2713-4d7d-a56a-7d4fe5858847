using UnityEngine;
#if ENABLE_INPUT_SYSTEM
using UnityEngine.InputSystem;
#endif

// ColdVor - Simple RTS camera controller for Unity 6
// NO MOVEMENT unless there is input. Camera stays exactly where you put it in editor.

[DisallowMultipleComponent]
[RequireComponent(typeof(Camera))]
public class RTSCameraController : MonoBehaviour
{
    [Header("Pan / Translation")]
    public float panSpeed = 20f;
    public KeyCode fastPanModifier = KeyCode.LeftShift;
    public float fastPanMultiplier = 2f;

    [Header("Edge Panning")]
    public bool edgePanning = true;
    public int edgeThickness = 12;

    [Header("Rotate (hold RMB)")]
    public float rotateSpeed = 120f;
    public float pitchSpeed = 120f;
    public float minPitch = 20f;
    public float maxPitch = 85f;
    public bool invertRotateX = false;
    public bool invertRotateY = true;

    [Header("Zoom (Scroll Wheel)")]
    public float zoomSpeed = 5f;

    [Header("Input Options")]
    public bool allowArrowKeys = true;
    public bool allowWASD = true;
    public bool allowMMBPan = true;

    void Update()
    {
        float dt = Time.deltaTime;

        // Read all input
        Vector3 panInput = ReadPanInput(dt);
        Vector2 rotateInput = ReadRotateInput(dt);
        float zoomInput = ReadZoomInput();

        // Only do ANYTHING if there's actual input
        if (panInput.sqrMagnitude > 0.001f || rotateInput.sqrMagnitude > 0.001f || Mathf.Abs(zoomInput) > 0.001f)
        {
            // Apply pan movement
            if (panInput.sqrMagnitude > 0.001f)
            {
                transform.position += panInput;
            }

            // Apply rotation
            if (rotateInput.sqrMagnitude > 0.001f)
            {
                Vector3 euler = transform.rotation.eulerAngles;
                float yaw = euler.y + rotateInput.x;
                float pitch = euler.x + rotateInput.y;
                if (pitch > 180f) pitch -= 360f;
                pitch = Mathf.Clamp(pitch, minPitch, maxPitch);
                transform.rotation = Quaternion.Euler(pitch, yaw, 0f);
            }

            // Apply zoom
            if (Mathf.Abs(zoomInput) > 0.001f)
            {
                Vector3 forward = transform.forward;
                transform.position += forward * zoomInput * zoomSpeed * dt;
            }
        }
    }

    Vector3 ReadPanInput(float dt)
    {
        Vector3 delta = Vector3.zero;

        // Keyboard input
        Vector2 input = Vector2.zero;
        if (allowWASD)
        {
            if (IsKeyHeld(KeyCode.W)) input.y += 1f;
            if (IsKeyHeld(KeyCode.S)) input.y -= 1f;
            if (IsKeyHeld(KeyCode.A)) input.x -= 1f;
            if (IsKeyHeld(KeyCode.D)) input.x += 1f;
        }
        if (allowArrowKeys)
        {
            if (IsKeyHeld(KeyCode.UpArrow)) input.y += 1f;
            if (IsKeyHeld(KeyCode.DownArrow)) input.y -= 1f;
            if (IsKeyHeld(KeyCode.LeftArrow)) input.x -= 1f;
            if (IsKeyHeld(KeyCode.RightArrow)) input.x += 1f;
        }

        // Edge panning
        if (edgePanning && !IsRMBHeld() && !IsMMBHeld())
        {
            Vector2 mousePos = GetMousePosition();
            if (mousePos.x <= edgeThickness) input.x -= 1f;
            if (mousePos.x >= Screen.width - edgeThickness) input.x += 1f;
            if (mousePos.y <= edgeThickness) input.y -= 1f;
            if (mousePos.y >= Screen.height - edgeThickness) input.y += 1f;
        }

        // MMB drag
        if (allowMMBPan && IsMMBHeld())
        {
            Vector2 mouseDelta = GetMouseDelta();
            input.x -= mouseDelta.x * 0.01f;
            input.y -= mouseDelta.y * 0.01f;
        }

        // Convert to world movement
        if (input.sqrMagnitude > 0.001f)
        {
            Vector3 right = transform.right; right.y = 0f; right.Normalize();
            Vector3 forward = transform.forward; forward.y = 0f; forward.Normalize();
            float speed = panSpeed * (IsKeyHeld(fastPanModifier) ? fastPanMultiplier : 1f);
            delta = (right * input.x + forward * input.y) * speed * dt;
        }

        return delta;
    }

    Vector2 ReadRotateInput(float dt)
    {
        if (!IsRMBHeld()) return Vector2.zero;

        Vector2 mouseDelta = GetMouseDelta();
        if (mouseDelta.sqrMagnitude < 0.001f) return Vector2.zero;

        float yawDelta = (invertRotateX ? -1f : 1f) * mouseDelta.x * rotateSpeed * dt;
        float pitchDelta = (invertRotateY ? -1f : 1f) * -mouseDelta.y * pitchSpeed * dt;

        return new Vector2(yawDelta, pitchDelta);
    }

    float ReadZoomInput()
    {
        return GetScrollDelta().y;
    }

    void EnforceGroundClearance()
    {
        // Keep camera at least minHeightAboveGround over ground
        Vector3 camPos = transform.position;
        if (Physics.Raycast(new Ray(camPos + Vector3.up * 0.5f, Vector3.down), out RaycastHit hit, 1000f, groundMask, QueryTriggerInteraction.Ignore))
        {
            float clearance = camPos.y - hit.point.y;
            if (clearance < minHeightAboveGround)
            {
                float pushUp = minHeightAboveGround - clearance;
                _targetPosition.y += pushUp;
                transform.position = _targetPosition;
            }
        }
    }



    // ---------- Input Abstraction (Old and New Systems) ----------
    bool IsKeyHeld(KeyCode key)
    {
#if ENABLE_INPUT_SYSTEM
        // Input System does not have KeyCode directly for all cases; map common ones
        // Use Keyboard.current fallback
        var kb = Keyboard.current;
        if (kb == null) return false;
        switch (key)
        {
            case KeyCode.W: return kb.wKey.isPressed;
            case KeyCode.A: return kb.aKey.isPressed;
            case KeyCode.S: return kb.sKey.isPressed;
            case KeyCode.D: return kb.dKey.isPressed;
            case KeyCode.LeftShift: return kb.leftShiftKey.isPressed;
            case KeyCode.RightShift: return kb.rightShiftKey.isPressed;
            case KeyCode.UpArrow: return kb.upArrowKey.isPressed;
            case KeyCode.DownArrow: return kb.downArrowKey.isPressed;
            case KeyCode.LeftArrow: return kb.leftArrowKey.isPressed;
            case KeyCode.RightArrow: return kb.rightArrowKey.isPressed;
            default:
                // Fallback to legacy Input for uncommon keys if available
                return Input.GetKey(key);
        }
#else
        return Input.GetKey(key);
#endif
    }

    bool IsRMBHeld()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null && Mouse.current.rightButton.isPressed;
#else
        return Input.GetMouseButton(1);
#endif
    }
    bool IsMMBHeld()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null && Mouse.current.middleButton.isPressed;
#else
        return Input.GetMouseButton(2);
#endif
    }

    Vector2 GetMouseDelta()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null ? Mouse.current.delta.ReadValue() : Vector2.zero;
#else
        return new Vector2(Input.GetAxisRaw("Mouse X"), Input.GetAxisRaw("Mouse Y"));
#endif
    }

    Vector2 GetScrollDelta()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null ? Mouse.current.scroll.ReadValue() : Vector2.zero;
#else
        return new Vector2(0f, Input.mouseScrollDelta.y);
#endif
    }

    Vector2 GetMousePosition()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null ? Mouse.current.position.ReadValue() : Vector2.negativeInfinity;
#else
        return Input.mousePosition;
#endif
    }
}

