[Licensing::Mo<PERSON><PERSON>] Trying to connect to existing licensing client channel...
Built from '6000.2/staging' branch; Version is '6000.2.1f1 (55300504c302) revision 5582853'; Using compiler version '194234433'; Build Type 'Release'
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-adamh" at "2025-08-23T21:28:36.5361803Z"
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65448 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-23T21:28:36Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.2.1f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Users/<USER>/ColdVor
-logFile
Logs/AssetImportWorker2.log
-srvPort
64452
-job-worker-count
11
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Users/<USER>/ColdVor
C:/Users/<USER>/ColdVor
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [31692]  Target information:

Player connection [31692]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1411068566 [EditorId] 1411068566 [Version] 1048832 [Id] WindowsEditor(7,AdamsPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [31692] Host joined multi-casting on [***********:54997]...
Player connection [31692] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
Input System module state changed to: Initialized.
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 16144, path: "C:/Program Files/Unity Hub/UnityLicensingClient_V1/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Error: HandshakeResponse reported an error:
	ResponseCode: 505
	ResponseStatus: Unsupported protocol version '1.17.2'.
[Licensing::Module] Error: Failed to handshake to channel: "LicenseClient-adamh"
[Licensing::IpcConnector] LicenseClient-adamh channel disconnected successfully.
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-adamh-6000.2.1" at "2025-08-23T21:28:36.5706778Z"
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 28024, path: "C:/Program Files/Unity/Hub/Editor/6000.2.1f1/Editor/Data/Resources/Licensing/Client/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.2+ff71f16
  Session Id:              0dad756d6ae24eb3bbbc0e86f0c693db
  Correlation Id:          4b223eb02eb08805f788b51fff91d26a
  External correlation Id: 8155859913994595727
  Machine Id:              xzpLgqfFtRWRr08+VTnPVDC62vo=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-adamh-6000.2.1" (connect: 0.00s, validation: 0.00s, handshake: 0.00s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-adamh-6000.2.1-notifications" at "2025-08-23T21:28:36.5764534Z"
[Licensing::Module] Licensing Background thread has ended after 0.04s
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.33 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.2.1f1 (55300504c302)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.2.1f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ColdVor/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
d3d12: failed to query info queue interface (0x80004002).
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA GeForce RTX 4070 Ti SUPER (ID=0x2705)
    Vendor:          NVIDIA
    VRAM:            16063 MB
    App VRAM Budget: 15295 MB
    Driver:          32.0.15.8108
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.2.1f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.2.1f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.2.1f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56440
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.1f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001953 seconds.
- Loaded All Assemblies, in  0.350 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.327 seconds
Domain Reload Profiling: 675ms
	BeginReloadAssembly (113ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (146ms)
		LoadAssemblies (110ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (143ms)
			TypeCache.Refresh (141ms)
				TypeCache.ScanAssembly (127ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (328ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (289ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (54ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (57ms)
			ProcessInitializeOnLoadAttributes (120ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: 1102596-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
- Loaded All Assemblies, in  0.638 seconds
Refreshing native plugins compatible for Editor in 0.94 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.474 seconds
Domain Reload Profiling: 1109ms
	BeginReloadAssembly (168ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (386ms)
		LoadAssemblies (274ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (203ms)
			TypeCache.Refresh (170ms)
				TypeCache.ScanAssembly (156ms)
			BuildScriptInfoCaches (27ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (474ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (378ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (271ms)
			ProcessInitializeOnLoadMethodAttributes (21ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 0.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3827 unused Assets / (0.9 MB). Loaded Objects now: 4316.
Memory consumption went from 83.9 MB to 83.0 MB.
Total: 5.367300 ms (FindLiveObjects: 0.345900 ms CreateObjectMapping: 0.363600 ms MarkObjects: 3.885600 ms  DeleteObjects: 0.771200 ms)

========================================================================
Received Import Request.
  Time since last request: 21465.484992 seconds.
  path: Assets/Better Landscape Pack Vol.1/Prefabs/Terrain_2.prefab
  artifactKey: Guid(1e1c458e95e9e9a4788eba2a75d6d902) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Prefabs/Terrain_2.prefab using Guid(1e1c458e95e9e9a4788eba2a75d6d902) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '34cd9562ba0bcccc2acbdd2697ecea1c') in 0.0340239 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
- Loaded All Assemblies, in  0.490 seconds
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.434 seconds
Domain Reload Profiling: 924ms
	BeginReloadAssembly (162ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (265ms)
		LoadAssemblies (239ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (106ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (434ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (345ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (240ms)
			ProcessInitializeOnLoadMethodAttributes (18ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3825 unused Assets / (1.0 MB). Loaded Objects now: 4320.
Memory consumption went from 86.7 MB to 85.7 MB.
Total: 5.890600 ms (FindLiveObjects: 0.394200 ms CreateObjectMapping: 0.421200 ms MarkObjects: 4.176800 ms  DeleteObjects: 0.897700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 150.380483 seconds.
  path: Packages/com.unity.ext.nunit/package.json
  artifactKey: Guid(8143d3a8390f2c64685e3bc272bd9e90) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.unity.ext.nunit/package.json using Guid(8143d3a8390f2c64685e3bc272bd9e90) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '195ad4416516d9f8d0e46a7eaad68a21') in 0.0148944 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 26.511170 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Prefabs
  artifactKey: Guid(053fc8aa86fb43c49937c7aae4020d51) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Prefabs using Guid(053fc8aa86fb43c49937c7aae4020d51) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '10454b08c9d983191c8cbf106d65622b') in 0.0003986 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.599114 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles
  artifactKey: Guid(c95a2c62502363d40a42ef26a6d92e6e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles using Guid(c95a2c62502363d40a42ef26a6d92e6e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9a4b35db0aacfc00e48e4d3eefc531bf') in 0.0003908 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.098056 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/Car1.prefab
  artifactKey: Guid(8d0d21aeddd059c49b1360e374d25e90) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/Car1.prefab using Guid(8d0d21aeddd059c49b1360e374d25e90) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dfe388497c4dfad9deb84c15f65ce921') in 0.2704314 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 43

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/Tank4.prefab
  artifactKey: Guid(01ff5922a65fd8044a2d469a6bbf78cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/Tank4.prefab using Guid(01ff5922a65fd8044a2d469a6bbf78cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ec7e0dae2ed0a8b106eb6a62713a67a3') in 0.1531498 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 186

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/Car4.prefab
  artifactKey: Guid(f8984b227fcdd504eb134ab7fdc6a6e0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/Car4.prefab using Guid(f8984b227fcdd504eb134ab7fdc6a6e0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4e4bf7d58e0ec60faa3417d37d1eda81') in 0.02137 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 55

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/Tank3.prefab
  artifactKey: Guid(d8db683777792a6438a4ba6a4d689966) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/Tank3.prefab using Guid(d8db683777792a6438a4ba6a4d689966) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '81019eb3e37e13d23480a6bfaff775f7') in 0.0300615 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 219

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/MilitaryTruck1.prefab
  artifactKey: Guid(0377d8a4649f5f240b648adb4a0a12b4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/MilitaryTruck1.prefab using Guid(0377d8a4649f5f240b648adb4a0a12b4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '78bbf44addc43327ecd3d4ce93fdcbf6') in 0.0426387 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 49

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/Tank1.prefab
  artifactKey: Guid(4f9eb76e1ac08dc47b7eaa2a6c6c9f29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/Tank1.prefab using Guid(4f9eb76e1ac08dc47b7eaa2a6c6c9f29) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1969973eab3a62999f67b2415162449d') in 0.0194309 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 205

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/FireSupport3.prefab
  artifactKey: Guid(38ca842476e47c44ebf71cef8dfa7c31) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/FireSupport3.prefab using Guid(38ca842476e47c44ebf71cef8dfa7c31) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7a5d1a60b7fdb4fd5654a7454c183992') in 0.020529 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 230

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/Tank5.prefab
  artifactKey: Guid(56bd6c87e70976a4e80d261523854cba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/Tank5.prefab using Guid(56bd6c87e70976a4e80d261523854cba) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eb13cb1167070bfbac6ae3e9bdc70d20') in 0.0186204 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 230

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/Car5.prefab
  artifactKey: Guid(9aa17e1faff7333438e756c78c847f73) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/Car5.prefab using Guid(9aa17e1faff7333438e756c78c847f73) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '451d34276c156a62c566f41f29256539') in 0.0228224 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 43

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/Car3.prefab
  artifactKey: Guid(2e285ffdf4efead4ab1e5e07f05e6020) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/Car3.prefab using Guid(2e285ffdf4efead4ab1e5e07f05e6020) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0ab7a83dd07599d39426108741141097') in 0.0226556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 55

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/FireSupport5.prefab
  artifactKey: Guid(57943b25be0741749b054462c2d8be74) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/FireSupport5.prefab using Guid(57943b25be0741749b054462c2d8be74) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '965a26df5b59ce61301dd1faca8997bd') in 0.0189041 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 230

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/LandArmored2.prefab
  artifactKey: Guid(a68dfb16531ee8f4b922697f4ad6018c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/LandArmored2.prefab using Guid(a68dfb16531ee8f4b922697f4ad6018c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b709e5a710c9eb1e34331ba120f4d6c9') in 0.0180964 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 49

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/LandArmored4.prefab
  artifactKey: Guid(bb17a203fa6690942ab4f9c1887b5920) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/LandArmored4.prefab using Guid(bb17a203fa6690942ab4f9c1887b5920) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '040fc4abe6e99b3993d3a0fffdde77c9') in 0.0258896 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 67

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/MilitaryTruck5.prefab
  artifactKey: Guid(5c5bba3859522f1488265f219e9ab9f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Prefabs/Vehicles/MilitaryTruck5.prefab using Guid(5c5bba3859522f1488265f219e9ab9f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '28f8c105cf4656254ab8a18769f7a9e4') in 0.0198083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 49

========================================================================
Received Import Request.
  Time since last request: 25.633502 seconds.
  path: Assets/Better Landscape Pack Vol.1/Terrains/New Terrain 1.asset
  artifactKey: Guid(9d0c92ac221535049b371f8a7cd4fa1e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Terrains/New Terrain 1.asset using Guid(9d0c92ac221535049b371f8a7cd4fa1e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '59c300fae4762c1edd2d48fd3a196925') in 0.0933693 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 70.876783 seconds.
  path: Assets/_TerrainAutoUpgrade/layer_3d3aba195e4d26ee.terrainlayer
  artifactKey: Guid(a5eb096729445734483965bb8689c984) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_TerrainAutoUpgrade/layer_3d3aba195e4d26ee.terrainlayer using Guid(a5eb096729445734483965bb8689c984) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bc83bdf370aa9967fff30d647aa520af') in 0.0093184 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 3.950261 seconds.
  path: Assets/Better Landscape Pack Vol.1/Standard Assets/SurfaceTextures/CliffAlbedoSpecular.psd
  artifactKey: Guid(18214e9d6af6248559d501391856f1c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Standard Assets/SurfaceTextures/CliffAlbedoSpecular.psd using Guid(18214e9d6af6248559d501391856f1c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '23fe515cefca6aba389d11b9a29fef5e') in 0.0162977 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_4.png
  artifactKey: Guid(6d7fcb5b7538de84fa3ed4670abcbaf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_4.png using Guid(6d7fcb5b7538de84fa3ed4670abcbaf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '50468724637f34afca3e8604d9fb0952') in 0.0214084 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_6.png
  artifactKey: Guid(aa3384e6379f4b0468e7720d14561bd8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_6.png using Guid(aa3384e6379f4b0468e7720d14561bd8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e16616e3c898db84787d20379b1bb63b') in 0.0177438 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_2.png
  artifactKey: Guid(2ed0833dd898f90418e397b8ff27bdf8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_2.png using Guid(2ed0833dd898f90418e397b8ff27bdf8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '48a8c05769f743e74f1c1602f409b70d') in 0.0248875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_7.png
  artifactKey: Guid(ca20919bb54136047b49a6bc0f95c095) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_7.png using Guid(ca20919bb54136047b49a6bc0f95c095) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9c08022a52aebc303f8d432390f465b8') in 0.0144343 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 1.055083 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_8.png
  artifactKey: Guid(f40e86c1f0074b84fb91dcf86ff8e99b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_8.png using Guid(f40e86c1f0074b84fb91dcf86ff8e99b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '84b3bfa5e27343ef6f14d2b2d29c2f87') in 0.0161798 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.027396 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_10.png
  artifactKey: Guid(553385dc9c9b0954db25f6d7557a0780) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_10.png using Guid(553385dc9c9b0954db25f6d7557a0780) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1c572f30d2db59257ce5608fc78d7eb0') in 0.0122807 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.025089 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_12.png
  artifactKey: Guid(f266701efdbf9714d8a314e82fbb060f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_12.png using Guid(f266701efdbf9714d8a314e82fbb060f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '49190ab4961b80017a61f0d493e60e72') in 0.0104378 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.362503 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_14.png
  artifactKey: Guid(33e6fca02d45db64e95766e612c5d91c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_14.png using Guid(33e6fca02d45db64e95766e612c5d91c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ea2b742c91fec82e917474412da65990') in 0.0171359 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.057902 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_16.png
  artifactKey: Guid(307046d9e35e1fd41a3f6de69d1c5d07) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_16.png using Guid(307046d9e35e1fd41a3f6de69d1c5d07) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e9738cc4c4280f54c1d2d3e8c4bcd9fc') in 0.0144558 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_18.png
  artifactKey: Guid(fef9597fc315af544a07f265ff9e7a59) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_18.png using Guid(fef9597fc315af544a07f265ff9e7a59) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9db1fccb6c7ee77232bcff87ccd792a0') in 0.0173063 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.307255 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_20.png
  artifactKey: Guid(6d2560cf149b63a45b72979d0e4b2ff6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_20.png using Guid(6d2560cf149b63a45b72979d0e4b2ff6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a494dd58f0a765dbfdcad3ac3b6a400b') in 0.0133641 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.026262 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_22.png
  artifactKey: Guid(2a20aed1c9818254cb7ff9cddb910527) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_22.png using Guid(2a20aed1c9818254cb7ff9cddb910527) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dc65d7c2b9778f5b1c5bf5aa6c60b058') in 0.0126838 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_25.png
  artifactKey: Guid(2d48f3c0228a75c4d9eee19815faf910) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Diffuse/diffuse_25.png using Guid(2d48f3c0228a75c4d9eee19815faf910) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'efbc02dd1682f63463231446df743499') in 0.0137383 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.011566 seconds.
  path: Assets/Better Landscape Pack Vol.1/Standard Assets/SurfaceTextures/GrassHillAlbedo.psd
  artifactKey: Guid(c6e0767b1f8c34890ac245217f4b9731) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Standard Assets/SurfaceTextures/GrassHillAlbedo.psd using Guid(c6e0767b1f8c34890ac245217f4b9731) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '101b9d44597c25d9a75f7ac6a61cd411') in 0.0149318 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.322533 seconds.
  path: Assets/Better Landscape Pack Vol.1/Standard Assets/SurfaceTextures/MudRockyAlbedoSpecular.bmp
  artifactKey: Guid(ef5c51cfa2ce46043a41a376b560c525) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Standard Assets/SurfaceTextures/MudRockyAlbedoSpecular.bmp using Guid(ef5c51cfa2ce46043a41a376b560c525) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd68ccb3677a2df4899a300ad4dc6b7a2') in 0.0154418 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_2.bmp
  artifactKey: Guid(a7758a311ca374d41b378d8d435e929e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_2.bmp using Guid(a7758a311ca374d41b378d8d435e929e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e134be70ff7212f4e4d6c786995dbc4d') in 0.0200815 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_4.bmp
  artifactKey: Guid(9547b5a4ef43af14ba0fc903dcd8f704) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_4.bmp using Guid(9547b5a4ef43af14ba0fc903dcd8f704) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f2adad30c76e46602191c33a3ea0bea') in 0.0109915 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_1.bmp
  artifactKey: Guid(9d67657161b63e94aa7f28fcf456dae1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_1.bmp using Guid(9d67657161b63e94aa7f28fcf456dae1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8f82fe7ef019aa99e15d92c3efd5349c') in 0.0108907 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_5.png
  artifactKey: Guid(c7f2de8f86654364884c6f4a9f05b715) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_5.png using Guid(c7f2de8f86654364884c6f4a9f05b715) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '320f3664f42c47b5c22b9614b2da128c') in 0.0137572 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.258891 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_7.png
  artifactKey: Guid(b114ff36d2d56ab4699dabd6d32fe567) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_7.png using Guid(b114ff36d2d56ab4699dabd6d32fe567) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fc002fec47a712c4b5a4cff3801302ac') in 0.0131334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.011577 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_9.bmp
  artifactKey: Guid(49d97b7f0d155fe4f9710c852dd4cf37) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_9.bmp using Guid(49d97b7f0d155fe4f9710c852dd4cf37) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '175d21d02259c2589b22578b65a512dd') in 0.0130131 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.024923 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_11.bmp
  artifactKey: Guid(115f154597a269a41b05cc50d25167ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_11.bmp using Guid(115f154597a269a41b05cc50d25167ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9ad590642ddbfff14a4a2a60d3735f0d') in 0.0126879 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_14.bmp
  artifactKey: Guid(905905a01b7a0bd47bf409cc21b2b803) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_14.bmp using Guid(905905a01b7a0bd47bf409cc21b2b803) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '653c2375ed1d2849a96cda0e6c5a25a7') in 0.0131759 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.302272 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_15.bmp
  artifactKey: Guid(5a203db033ef6e345977b83690e07de0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_15.bmp using Guid(5a203db033ef6e345977b83690e07de0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'da2eda093859951e04ccc43c7271c57a') in 0.0140066 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_17.bmp
  artifactKey: Guid(58d64c34afc7431469c4c11f4dfd5414) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_17.bmp using Guid(58d64c34afc7431469c4c11f4dfd5414) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '724ba5ed27e59a743b0b71bf677baa4b') in 0.0125917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.021260 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_19.bmp
  artifactKey: Guid(e5453659ebabfc14891db83aa27cae4b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_19.bmp using Guid(e5453659ebabfc14891db83aa27cae4b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7e85da8785d930981d08afb52ba2f771') in 0.0111513 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.027869 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_23.bmp
  artifactKey: Guid(5336c3dce28b6da429a1911517d8d055) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_23.bmp using Guid(5336c3dce28b6da429a1911517d8d055) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b478c815119b92cd5b4deb11b742a442') in 0.0119528 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.253947 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_25.bmp
  artifactKey: Guid(4a15346737f22324a9f766416bd71d60) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/Normal Maps/Normal_map_25.bmp using Guid(4a15346737f22324a9f766416bd71d60) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7df9d14f9b76f96233c5fffd767d2a91') in 0.0131766 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.021778 seconds.
  path: Assets/Better Landscape Pack Vol.1/Standard Assets/Skyboxes/Textures/Overcast1/Overcast1_down.tif
  artifactKey: Guid(454585cca07f953479b902a00ead3015) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Standard Assets/Skyboxes/Textures/Overcast1/Overcast1_down.tif using Guid(454585cca07f953479b902a00ead3015) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '61986da9e51ebbb495db798c90c8e631') in 0.0178789 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.242007 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Materials/Palette_s.psd
  artifactKey: Guid(61b274aed62eec54a9f709041a0bf56b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Materials/Palette_s.psd using Guid(61b274aed62eec54a9f709041a0bf56b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f9613b6fedc38480ed05bcd4dfbb8a5d') in 0.0119333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.019666 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Scenes/MilitaryBase/ReflectionProbe-0.exr
  artifactKey: Guid(163a6be9d4a4df5478df72408292fcac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Scenes/MilitaryBase/ReflectionProbe-0.exr using Guid(163a6be9d4a4df5478df72408292fcac) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0f913141163fa1215594d08601ff0234') in 0.0382025 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.022221 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/RGB/RGB_6.png
  artifactKey: Guid(7da549f07dd1d71488eae26d7c76c60f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/RGB/RGB_6.png using Guid(7da549f07dd1d71488eae26d7c76c60f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0120a6d67452b26a9ce6c4549eea4e48') in 0.0193148 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.249133 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/RGB/RGB_8.png
  artifactKey: Guid(f9873057d459d9843b3ba89aacf08a79) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/RGB/RGB_8.png using Guid(f9873057d459d9843b3ba89aacf08a79) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e14ddc09bca3817ffaa7a4977e969fb3') in 0.0246487 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.024355 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/RGB/RGB_12.png
  artifactKey: Guid(afa8389de6915434cb91bd4f985ddba6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/RGB/RGB_12.png using Guid(afa8389de6915434cb91bd4f985ddba6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c7a2d5b469b78d47da9028e97b244007') in 0.0247365 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.273685 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/RGB/RGB_16.png
  artifactKey: Guid(b80ddb758563bbf459d86b1ee94fedcf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/RGB/RGB_16.png using Guid(b80ddb758563bbf459d86b1ee94fedcf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f6520c5f030db915554bb665108037c5') in 0.0208522 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.037866 seconds.
  path: Assets/Better Landscape Pack Vol.1/Textures/RGB/RGB_20.png
  artifactKey: Guid(6fd6801e6c69b7741b1f7857b3262f91) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Textures/RGB/RGB_20.png using Guid(6fd6801e6c69b7741b1f7857b3262f91) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0f4a45692223633c065e23b758cac9bb') in 0.0254111 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.271066 seconds.
  path: Assets/Better Landscape Pack Vol.1/Standard Assets/SurfaceTextures/SandAlbedo.psd
  artifactKey: Guid(bfd675cc0db1d4656b75dc6d6ba91142) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Better Landscape Pack Vol.1/Standard Assets/SurfaceTextures/SandAlbedo.psd using Guid(bfd675cc0db1d4656b75dc6d6ba91142) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '27669184a71f35ad4e2391655356aa10') in 0.0124462 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 1.080088 seconds.
  path: Assets/StylizedMilitaryVehiclesPack/Materials/Trucks.psd
  artifactKey: Guid(7a818c610b1765648808737a6dcc0897) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StylizedMilitaryVehiclesPack/Materials/Trucks.psd using Guid(7a818c610b1765648808737a6dcc0897) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '89a25662e252d5992499b6545709c0b0') in 0.0136629 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.85 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3821 unused Assets / (2.1 MB). Loaded Objects now: 4365.
Memory consumption went from 101.2 MB to 99.1 MB.
Total: 6.196000 ms (FindLiveObjects: 0.366100 ms CreateObjectMapping: 0.170400 ms MarkObjects: 4.472500 ms  DeleteObjects: 1.186400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
- Loaded All Assemblies, in  0.471 seconds
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.420 seconds
Domain Reload Profiling: 890ms
	BeginReloadAssembly (154ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (257ms)
		LoadAssemblies (228ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (420ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (334ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (231ms)
			ProcessInitializeOnLoadMethodAttributes (17ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3825 unused Assets / (1.6 MB). Loaded Objects now: 4364.
Memory consumption went from 100.1 MB to 98.5 MB.
Total: 5.389900 ms (FindLiveObjects: 0.325300 ms CreateObjectMapping: 0.309000 ms MarkObjects: 3.730300 ms  DeleteObjects: 1.024600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
- Loaded All Assemblies, in  0.468 seconds
Refreshing native plugins compatible for Editor in 0.78 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.433 seconds
Domain Reload Profiling: 901ms
	BeginReloadAssembly (153ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (256ms)
		LoadAssemblies (225ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (434ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (345ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (241ms)
			ProcessInitializeOnLoadMethodAttributes (18ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.95 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.4 MB). Loaded Objects now: 4365.
Memory consumption went from 100.3 MB to 98.9 MB.
Total: 4.621100 ms (FindLiveObjects: 0.339600 ms CreateObjectMapping: 0.154400 ms MarkObjects: 3.339100 ms  DeleteObjects: 0.787600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 166.188972 seconds.
  path: Assets/_TerrainAutoUpgrade/layer_3d3aba195e4d26ee.terrainlayer
  artifactKey: Guid(a5eb096729445734483965bb8689c984) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_TerrainAutoUpgrade/layer_3d3aba195e4d26ee.terrainlayer using Guid(a5eb096729445734483965bb8689c984) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '052aad9dee35d00d12a860bd21f73726') in 0.0409026 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 18.469408 seconds.
  path: Assets/_TerrainAutoUpgrade/layer_3876d7db3bbaa70.terrainlayer
  artifactKey: Guid(9b6dfbb304928484fb73c3adcb822c0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_TerrainAutoUpgrade/layer_3876d7db3bbaa70.terrainlayer using Guid(9b6dfbb304928484fb73c3adcb822c0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f88173dc836627920e7eed21bd247c2a') in 0.0011345 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
- Loaded All Assemblies, in  0.478 seconds
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.419 seconds
Domain Reload Profiling: 896ms
	BeginReloadAssembly (157ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (256ms)
		LoadAssemblies (230ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (419ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (332ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (228ms)
			ProcessInitializeOnLoadMethodAttributes (17ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.5 MB). Loaded Objects now: 4365.
Memory consumption went from 99.9 MB to 98.5 MB.
Total: 4.678000 ms (FindLiveObjects: 0.329700 ms CreateObjectMapping: 0.157900 ms MarkObjects: 3.370800 ms  DeleteObjects: 0.818700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.477 seconds
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.413 seconds
Domain Reload Profiling: 889ms
	BeginReloadAssembly (159ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (258ms)
		LoadAssemblies (232ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (103ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (413ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (327ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (226ms)
			ProcessInitializeOnLoadMethodAttributes (17ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.4 MB). Loaded Objects now: 4365.
Memory consumption went from 100.1 MB to 98.7 MB.
Total: 4.636400 ms (FindLiveObjects: 0.335600 ms CreateObjectMapping: 0.154900 ms MarkObjects: 3.341800 ms  DeleteObjects: 0.803700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
- Loaded All Assemblies, in  0.463 seconds
Refreshing native plugins compatible for Editor in 0.88 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.415 seconds
Domain Reload Profiling: 877ms
	BeginReloadAssembly (152ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (253ms)
		LoadAssemblies (223ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (415ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (330ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (227ms)
			ProcessInitializeOnLoadMethodAttributes (17ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.5 MB). Loaded Objects now: 4365.
Memory consumption went from 100.3 MB to 98.8 MB.
Total: 4.609000 ms (FindLiveObjects: 0.326300 ms CreateObjectMapping: 0.154100 ms MarkObjects: 3.314600 ms  DeleteObjects: 0.813300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.468 seconds
Refreshing native plugins compatible for Editor in 0.88 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.418 seconds
Domain Reload Profiling: 886ms
	BeginReloadAssembly (153ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (256ms)
		LoadAssemblies (228ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (419ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (332ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (231ms)
			ProcessInitializeOnLoadMethodAttributes (17ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.4 MB). Loaded Objects now: 4365.
Memory consumption went from 100.5 MB to 99.0 MB.
Total: 4.649200 ms (FindLiveObjects: 0.342100 ms CreateObjectMapping: 0.154900 ms MarkObjects: 3.317000 ms  DeleteObjects: 0.834800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.478 seconds
Refreshing native plugins compatible for Editor in 0.91 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.421 seconds
Domain Reload Profiling: 898ms
	BeginReloadAssembly (155ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (262ms)
		LoadAssemblies (238ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (102ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (90ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (421ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (330ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (227ms)
			ProcessInitializeOnLoadMethodAttributes (18ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.4 MB). Loaded Objects now: 4365.
Memory consumption went from 100.6 MB to 99.2 MB.
Total: 5.082000 ms (FindLiveObjects: 0.328000 ms CreateObjectMapping: 0.324000 ms MarkObjects: 3.571600 ms  DeleteObjects: 0.857700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
- Loaded All Assemblies, in  0.472 seconds
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.413 seconds
Domain Reload Profiling: 885ms
	BeginReloadAssembly (160ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (253ms)
		LoadAssemblies (225ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (413ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (327ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (225ms)
			ProcessInitializeOnLoadMethodAttributes (17ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.5 MB). Loaded Objects now: 4365.
Memory consumption went from 100.8 MB to 99.3 MB.
Total: 4.610500 ms (FindLiveObjects: 0.327300 ms CreateObjectMapping: 0.161200 ms MarkObjects: 3.302000 ms  DeleteObjects: 0.819600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.461 seconds
Refreshing native plugins compatible for Editor in 1.12 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.413 seconds
Domain Reload Profiling: 873ms
	BeginReloadAssembly (153ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (247ms)
		LoadAssemblies (218ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (414ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (327ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (225ms)
			ProcessInitializeOnLoadMethodAttributes (17ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.5 MB). Loaded Objects now: 4365.
Memory consumption went from 100.9 MB to 99.4 MB.
Total: 5.119400 ms (FindLiveObjects: 0.330300 ms CreateObjectMapping: 0.157700 ms MarkObjects: 3.706100 ms  DeleteObjects: 0.924300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
- Loaded All Assemblies, in  0.461 seconds
Refreshing native plugins compatible for Editor in 0.91 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.422 seconds
Domain Reload Profiling: 883ms
	BeginReloadAssembly (152ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (251ms)
		LoadAssemblies (223ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (103ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (422ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (335ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (234ms)
			ProcessInitializeOnLoadMethodAttributes (17ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.85 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.4 MB). Loaded Objects now: 4365.
Memory consumption went from 101.1 MB to 99.7 MB.
Total: 4.657800 ms (FindLiveObjects: 0.315000 ms CreateObjectMapping: 0.169900 ms MarkObjects: 3.390300 ms  DeleteObjects: 0.782000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
- Loaded All Assemblies, in  0.477 seconds
Refreshing native plugins compatible for Editor in 0.83 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.415 seconds
Domain Reload Profiling: 892ms
	BeginReloadAssembly (158ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (258ms)
		LoadAssemblies (232ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (106ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (416ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (329ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (226ms)
			ProcessInitializeOnLoadMethodAttributes (18ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.5 MB). Loaded Objects now: 4365.
Memory consumption went from 101.3 MB to 99.9 MB.
Total: 4.577600 ms (FindLiveObjects: 0.330000 ms CreateObjectMapping: 0.156300 ms MarkObjects: 3.289500 ms  DeleteObjects: 0.801200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.469 seconds
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.414 seconds
Domain Reload Profiling: 883ms
	BeginReloadAssembly (152ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (257ms)
		LoadAssemblies (229ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (415ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (327ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (226ms)
			ProcessInitializeOnLoadMethodAttributes (17ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 0.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.6 MB). Loaded Objects now: 4365.
Memory consumption went from 101.5 MB to 99.9 MB.
Total: 4.622300 ms (FindLiveObjects: 0.326400 ms CreateObjectMapping: 0.154800 ms MarkObjects: 3.269300 ms  DeleteObjects: 0.871400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.475 seconds
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.417 seconds
Domain Reload Profiling: 891ms
	BeginReloadAssembly (153ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (263ms)
		LoadAssemblies (230ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (111ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (99ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (417ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (329ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (227ms)
			ProcessInitializeOnLoadMethodAttributes (18ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.6 MB). Loaded Objects now: 4365.
Memory consumption went from 101.7 MB to 100.1 MB.
Total: 5.447500 ms (FindLiveObjects: 0.326000 ms CreateObjectMapping: 0.318700 ms MarkObjects: 3.763500 ms  DeleteObjects: 1.038800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.462 seconds
Refreshing native plugins compatible for Editor in 0.83 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.425 seconds
Domain Reload Profiling: 887ms
	BeginReloadAssembly (162ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (241ms)
		LoadAssemblies (224ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (100ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (90ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (426ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (338ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (237ms)
			ProcessInitializeOnLoadMethodAttributes (17ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.5 MB). Loaded Objects now: 4365.
Memory consumption went from 101.8 MB to 100.4 MB.
Total: 4.587700 ms (FindLiveObjects: 0.323200 ms CreateObjectMapping: 0.154200 ms MarkObjects: 3.312600 ms  DeleteObjects: 0.797300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
- Loaded All Assemblies, in  0.461 seconds
Refreshing native plugins compatible for Editor in 0.76 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.415 seconds
Domain Reload Profiling: 876ms
	BeginReloadAssembly (158ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (245ms)
		LoadAssemblies (217ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (103ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (415ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (329ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (226ms)
			ProcessInitializeOnLoadMethodAttributes (19ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.5 MB). Loaded Objects now: 4365.
Memory consumption went from 102.0 MB to 100.5 MB.
Total: 4.991100 ms (FindLiveObjects: 0.326300 ms CreateObjectMapping: 0.153700 ms MarkObjects: 3.646300 ms  DeleteObjects: 0.864200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.473 seconds
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.417 seconds
Domain Reload Profiling: 889ms
	BeginReloadAssembly (150ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (264ms)
		LoadAssemblies (225ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (114ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (101ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (417ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (330ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (227ms)
			ProcessInitializeOnLoadMethodAttributes (17ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.5 MB). Loaded Objects now: 4365.
Memory consumption went from 102.2 MB to 100.7 MB.
Total: 4.718300 ms (FindLiveObjects: 0.329200 ms CreateObjectMapping: 0.155900 ms MarkObjects: 3.387700 ms  DeleteObjects: 0.844900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.469 seconds
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.417 seconds
Domain Reload Profiling: 886ms
	BeginReloadAssembly (154ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (256ms)
		LoadAssemblies (230ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (103ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (418ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (329ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (227ms)
			ProcessInitializeOnLoadMethodAttributes (18ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.5 MB). Loaded Objects now: 4365.
Memory consumption went from 102.4 MB to 100.8 MB.
Total: 5.117800 ms (FindLiveObjects: 0.378400 ms CreateObjectMapping: 0.198200 ms MarkObjects: 3.623600 ms  DeleteObjects: 0.917000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.475 seconds
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.416 seconds
Domain Reload Profiling: 890ms
	BeginReloadAssembly (157ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (259ms)
		LoadAssemblies (237ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (103ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (416ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (328ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (226ms)
			ProcessInitializeOnLoadMethodAttributes (17ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.95 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.5 MB). Loaded Objects now: 4365.
Memory consumption went from 102.5 MB to 101.0 MB.
Total: 4.772300 ms (FindLiveObjects: 0.334300 ms CreateObjectMapping: 0.282000 ms MarkObjects: 3.287500 ms  DeleteObjects: 0.868100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.470 seconds
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.420 seconds
Domain Reload Profiling: 889ms
	BeginReloadAssembly (155ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (256ms)
		LoadAssemblies (231ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (103ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (420ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (333ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (227ms)
			ProcessInitializeOnLoadMethodAttributes (17ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.5 MB). Loaded Objects now: 4365.
Memory consumption went from 102.7 MB to 101.2 MB.
Total: 4.865400 ms (FindLiveObjects: 0.332400 ms CreateObjectMapping: 0.318300 ms MarkObjects: 3.297300 ms  DeleteObjects: 0.916900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
- Loaded All Assemblies, in  0.473 seconds
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.414 seconds
Domain Reload Profiling: 886ms
	BeginReloadAssembly (153ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (258ms)
		LoadAssemblies (226ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (106ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (94ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (415ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (327ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (227ms)
			ProcessInitializeOnLoadMethodAttributes (18ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3826 unused Assets / (1.5 MB). Loaded Objects now: 4365.
Memory consumption went from 102.9 MB to 101.3 MB.
Total: 4.757700 ms (FindLiveObjects: 0.324400 ms CreateObjectMapping: 0.157200 ms MarkObjects: 3.385300 ms  DeleteObjects: 0.890400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 838.962633 seconds.
  path: Assets/_TerrainAutoUpgrade/layer_3876d7db3bbaa70.terrainlayer
  artifactKey: Guid(9b6dfbb304928484fb73c3adcb822c0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_TerrainAutoUpgrade/layer_3876d7db3bbaa70.terrainlayer using Guid(9b6dfbb304928484fb73c3adcb822c0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cf3d7a94be57a6b099adf2b0cb58d88e') in 0.0419819 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 21.597169 seconds.
  path: Assets
  artifactKey: Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets using Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c391582d739b6e62a5220f35cbb167ac') in 0.0003845 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
- Loaded All Assemblies, in  1.720 seconds
Refreshing native plugins compatible for Editor in 0.98 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.445 seconds
Domain Reload Profiling: 2163ms
	BeginReloadAssembly (163ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (1492ms)
		LoadAssemblies (1467ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (109ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (95ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (446ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (354ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (244ms)
			ProcessInitializeOnLoadMethodAttributes (19ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.99 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3827 unused Assets / (1.4 MB). Loaded Objects now: 4366.
Memory consumption went from 100.0 MB to 98.6 MB.
Total: 4.769900 ms (FindLiveObjects: 0.349200 ms CreateObjectMapping: 0.173700 ms MarkObjects: 3.349700 ms  DeleteObjects: 0.896500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 439.584585 seconds.
  path: Assets/Scripts
  artifactKey: Guid(6f8d12779ec70ad49b8c6f98a92e51e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts using Guid(6f8d12779ec70ad49b8c6f98a92e51e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '604cf669a5bb65d99e3dfba68cef15d0') in 0.0020571 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.510 seconds
Refreshing native plugins compatible for Editor in 1.03 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.438 seconds
Domain Reload Profiling: 947ms
	BeginReloadAssembly (161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (286ms)
		LoadAssemblies (245ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (123ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (110ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (439ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (349ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (238ms)
			ProcessInitializeOnLoadMethodAttributes (19ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 1.01 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3827 unused Assets / (1.4 MB). Loaded Objects now: 4366.
Memory consumption went from 100.0 MB to 98.6 MB.
Total: 4.807800 ms (FindLiveObjects: 0.337000 ms CreateObjectMapping: 0.160900 ms MarkObjects: 3.497500 ms  DeleteObjects: 0.811900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
- Loaded All Assemblies, in  0.502 seconds
Refreshing native plugins compatible for Editor in 1.08 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.448 seconds
Domain Reload Profiling: 949ms
	BeginReloadAssembly (164ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (276ms)
		LoadAssemblies (248ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (110ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (98ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (448ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (357ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (248ms)
			ProcessInitializeOnLoadMethodAttributes (18ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 1.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3827 unused Assets / (1.3 MB). Loaded Objects now: 4366.
Memory consumption went from 100.2 MB to 98.8 MB.
Total: 5.370600 ms (FindLiveObjects: 0.359200 ms CreateObjectMapping: 0.358300 ms MarkObjects: 3.798800 ms  DeleteObjects: 0.853600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.501 seconds
Refreshing native plugins compatible for Editor in 0.90 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.448 seconds
Domain Reload Profiling: 948ms
	BeginReloadAssembly (164ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (273ms)
		LoadAssemblies (243ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (114ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (101ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (449ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (354ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (243ms)
			ProcessInitializeOnLoadMethodAttributes (19ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 1.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3827 unused Assets / (1.5 MB). Loaded Objects now: 4366.
Memory consumption went from 100.3 MB to 98.9 MB.
Total: 5.471200 ms (FindLiveObjects: 0.337200 ms CreateObjectMapping: 0.318300 ms MarkObjects: 3.891200 ms  DeleteObjects: 0.924000 ms)

Prepare: number of updated asset objects reloaded= 0
