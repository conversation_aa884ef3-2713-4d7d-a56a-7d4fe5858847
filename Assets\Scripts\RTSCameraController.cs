using UnityEngine;
#if ENABLE_INPUT_SYSTEM
using UnityEngine.InputSystem;
#endif

// ColdVor - RTS-style camera controller for Unity 6
// Attach to Main Camera. Direct camera control without pivot system.
// Supports: WASD/Arrows, edge panning, MMB drag-pan, RMB rotate (yaw/pitch), scroll zoom,
// zoom-speed scaling, height clamping above ground, optional world bounds.

[DisallowMultipleComponent]
[RequireComponent(typeof(Camera))]
public class RTSCameraController : MonoBehaviour
{
    [Header("Pan / Translation")]
    [Tooltip("Base ground-plane pan speed in units per second.")]
    public float panSpeed = 20f;
    [Tooltip("Hold to pan faster.")]
    public KeyCode fastPanModifier = KeyCode.LeftShift;
    public float fastPanMultiplier = 2f;

    [Header("Edge Panning")]
    public bool edgePanning = true;
    [Tooltip("Thickness in pixels along screen edges to trigger panning.")]
    public int edgeThickness = 12;

    [Header("Rotate (hold RMB)")]
    public float rotateSpeed = 120f; // degrees/sec per mouse unit
    public float pitchSpeed = 120f;  // degrees/sec per mouse unit
    public float minPitch = 20f;
    public float maxPitch = 85f;
    public bool invertRotateX = false;
    public bool invertRotateY = true;

    [Header("Zoom (Scroll Wheel)")]
    public float zoomSpeed = 5f;          // movement speed when zooming
    public float zoomSmoothing = 0.08f;   // smaller = snappier

    [Header("Smoothing")]
    public float moveSmoothing = 0.08f;   // smaller = snappier
    public float rotateSmoothing = 0.08f; // smaller = snappier

    [Header("Ground & Height")]
    [Tooltip("Layers considered ground for height clamping.")]
    public LayerMask groundMask = ~0;
    [Tooltip("Minimum clearance the camera should keep above ground (in meters).")]
    public float minHeightAboveGround = 1.5f;
    [Tooltip("Enable automatic ground clearance enforcement.")]
    public bool enforceGroundClearance = false;

    [Header("World Bounds (optional)")]
    public bool useWorldBounds = false;
    public Vector2 worldXLimits = new Vector2(-500f, 500f);
    public Vector2 worldZLimits = new Vector2(-500f, 500f);

    [Header("Input Options")]
    public bool allowArrowKeys = true;
    public bool allowWASD = true;
    public bool allowMMBPan = true; // hold Middle Mouse to drag-pan

    // Internal state
    private Camera _cam;
    private float _yaw;                  // degrees
    private float _pitch;                // degrees

    // Targets for smoothing
    private Vector3 _targetPosition;
    private float _targetYaw;
    private float _targetPitch;

    // SmoothDamp velocity caches
    private Vector3 _positionVel;
    private float _yawVel;
    private float _pitchVel;

    void Awake()
    {
        _cam = GetComponent<Camera>();

        // Initialize from camera's current orientation in Unity editor - NO MOVEMENT
        Vector3 euler = transform.rotation.eulerAngles;
        _yaw = _targetYaw = euler.y;
        float pitch = euler.x;
        if (pitch > 180f) pitch -= 360f;
        _pitch = _targetPitch = Mathf.Clamp(pitch, minPitch, maxPitch);

        // Initialize target position to current position - NO MOVEMENT
        _targetPosition = transform.position;
    }

    void Update()
    {
        float dt = Time.unscaledDeltaTime; // camera should remain responsive even if timeScale changes

        // 1) Input -> desired deltas
        Vector3 panDelta = ReadPanInput(dt);
        ReadRotateInput(dt, out float dYaw, out float dPitch);
        float zoomDelta = ReadZoomInput();

        // Check if there's any input at all
        bool hasInput = panDelta.sqrMagnitude > 0.001f || Mathf.Abs(dYaw) > 0.001f || Mathf.Abs(dPitch) > 0.001f || Mathf.Abs(zoomDelta) > 0.001f;

        // Only update if there's input
        if (hasInput)
        {
            // 2) Apply to targets
            _targetPosition += panDelta;
            _targetYaw += dYaw;
            _targetPitch = Mathf.Clamp(_targetPitch + dPitch, minPitch, maxPitch);

            // Zoom by moving forward/backward
            if (Mathf.Abs(zoomDelta) > 0.001f)
            {
                Vector3 forward = Quaternion.Euler(_targetPitch, _targetYaw, 0f) * Vector3.forward;
                _targetPosition += forward * zoomDelta * zoomSpeed * dt;
            }

            // 3) Optional world bounds
            if (useWorldBounds)
            {
                _targetPosition.x = Mathf.Clamp(_targetPosition.x, worldXLimits.x, worldXLimits.y);
                _targetPosition.z = Mathf.Clamp(_targetPosition.z, worldZLimits.x, worldZLimits.y);
            }

            // 4) Smooth towards targets
            _yaw = Mathf.SmoothDampAngle(_yaw, _targetYaw, ref _yawVel, rotateSmoothing, Mathf.Infinity, dt);
            _pitch = Mathf.SmoothDamp(_pitch, _targetPitch, ref _pitchVel, rotateSmoothing, Mathf.Infinity, dt);
            transform.position = Vector3.SmoothDamp(transform.position, _targetPosition, ref _positionVel, moveSmoothing, Mathf.Infinity, dt);

            // 5) Apply rotation
            transform.rotation = Quaternion.Euler(_pitch, _yaw, 0f);
        }

        // 6) Height clamping above ground (post-apply)
        if (enforceGroundClearance)
        {
            EnforceGroundClearance();
        }
    }

    Vector3 ReadPanInput(float dt)
    {
        Vector3 delta = Vector3.zero;

        // Keyboard pan
        Vector2 kb = Vector2.zero;
        if (allowWASD)
        {
            kb.x += IsKeyHeld(KeyCode.D) ? 1f : 0f;
            kb.x -= IsKeyHeld(KeyCode.A) ? 1f : 0f;
            kb.y += IsKeyHeld(KeyCode.W) ? 1f : 0f;
            kb.y -= IsKeyHeld(KeyCode.S) ? 1f : 0f;
        }
        if (allowArrowKeys)
        {
            kb.x += IsKeyHeld(KeyCode.RightArrow) ? 1f : 0f;
            kb.x -= IsKeyHeld(KeyCode.LeftArrow) ? 1f : 0f;
            kb.y += IsKeyHeld(KeyCode.UpArrow) ? 1f : 0f;
            kb.y -= IsKeyHeld(KeyCode.DownArrow) ? 1f : 0f;
        }
        kb = Vector2.ClampMagnitude(kb, 1f);

        // Edge panning (only when not rotating/MMB-dragging)
        if (edgePanning && !IsRMBHeld() && !IsMMBHeld())
        {
            Vector2 mp = GetMousePosition();
            if (mp.x <= edgeThickness) kb.x -= 1f;
            else if (mp.x >= Screen.width - edgeThickness) kb.x += 1f;
            if (mp.y <= edgeThickness) kb.y -= 1f;
            else if (mp.y >= Screen.height - edgeThickness) kb.y += 1f;
        }

        // Translate in camera-space projected onto XZ ground
        if (kb.sqrMagnitude > 0f)
        {
            // Get camera's forward and right vectors, projected onto ground plane
            Vector3 camForward = transform.forward; camForward.y = 0f; camForward.Normalize();
            Vector3 camRight = transform.right; camRight.y = 0f; camRight.Normalize();
            float speed = panSpeed * (IsKeyHeld(fastPanModifier) ? fastPanMultiplier : 1f);
            delta += (camRight * kb.x + camForward * kb.y) * speed * dt;
        }

        // MMB drag pan
        if (allowMMBPan && IsMMBHeld())
        {
            Vector2 md = GetMouseDelta();
            if (md.sqrMagnitude > 0f)
            {
                // Drag in screen space -> move along ground plane relative to camera
                Vector3 camRight = transform.right; camRight.y = 0f; camRight.Normalize();
                Vector3 camForward = transform.forward; camForward.y = 0f; camForward.Normalize();
                float dragScale = panSpeed * 0.03f;
                delta += (-camRight * md.x + -camForward * md.y) * dragScale; // drag feels like "grabbing" the world
            }
        }

        return delta;
    }

    void ReadRotateInput(float dt, out float dYaw, out float dPitch)
    {
        dYaw = 0f; dPitch = 0f;
        if (!IsRMBHeld()) return;
        Vector2 md = GetMouseDelta();
        if (md.sqrMagnitude <= 0f) return;
        float sx = invertRotateX ? -1f : 1f;
        float sy = invertRotateY ? -1f : 1f;
        dYaw = sx * md.x * rotateSpeed * dt;
        dPitch = sy * -md.y * pitchSpeed * dt;
    }

    float ReadZoomInput()
    {
        float scroll = GetScrollDelta().y;
        return scroll; // processed later with zoomSpeed
    }

    void EnforceGroundClearance()
    {
        // Keep camera at least minHeightAboveGround over ground
        Vector3 camPos = transform.position;
        if (Physics.Raycast(new Ray(camPos + Vector3.up * 0.5f, Vector3.down), out RaycastHit hit, 1000f, groundMask, QueryTriggerInteraction.Ignore))
        {
            float clearance = camPos.y - hit.point.y;
            if (clearance < minHeightAboveGround)
            {
                float pushUp = minHeightAboveGround - clearance;
                _targetPosition.y += pushUp;
                transform.position = _targetPosition;
            }
        }
    }



    // ---------- Input Abstraction (Old and New Systems) ----------
    bool IsKeyHeld(KeyCode key)
    {
#if ENABLE_INPUT_SYSTEM
        // Input System does not have KeyCode directly for all cases; map common ones
        // Use Keyboard.current fallback
        var kb = Keyboard.current;
        if (kb == null) return false;
        switch (key)
        {
            case KeyCode.W: return kb.wKey.isPressed;
            case KeyCode.A: return kb.aKey.isPressed;
            case KeyCode.S: return kb.sKey.isPressed;
            case KeyCode.D: return kb.dKey.isPressed;
            case KeyCode.LeftShift: return kb.leftShiftKey.isPressed;
            case KeyCode.RightShift: return kb.rightShiftKey.isPressed;
            case KeyCode.UpArrow: return kb.upArrowKey.isPressed;
            case KeyCode.DownArrow: return kb.downArrowKey.isPressed;
            case KeyCode.LeftArrow: return kb.leftArrowKey.isPressed;
            case KeyCode.RightArrow: return kb.rightArrowKey.isPressed;
            default:
                // Fallback to legacy Input for uncommon keys if available
                return Input.GetKey(key);
        }
#else
        return Input.GetKey(key);
#endif
    }

    bool IsRMBHeld()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null && Mouse.current.rightButton.isPressed;
#else
        return Input.GetMouseButton(1);
#endif
    }
    bool IsMMBHeld()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null && Mouse.current.middleButton.isPressed;
#else
        return Input.GetMouseButton(2);
#endif
    }

    Vector2 GetMouseDelta()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null ? Mouse.current.delta.ReadValue() : Vector2.zero;
#else
        return new Vector2(Input.GetAxisRaw("Mouse X"), Input.GetAxisRaw("Mouse Y"));
#endif
    }

    Vector2 GetScrollDelta()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null ? Mouse.current.scroll.ReadValue() : Vector2.zero;
#else
        return new Vector2(0f, Input.mouseScrollDelta.y);
#endif
    }

    Vector2 GetMousePosition()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null ? Mouse.current.position.ReadValue() : Vector2.negativeInfinity;
#else
        return Input.mousePosition;
#endif
    }
}

