{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 33816, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 33816, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 33816, "tid": 6526, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 33816, "tid": 6526, "ts": 1755986867697411, "dur": 526, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 33816, "tid": 6526, "ts": 1755986867700973, "dur": 658, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 33816, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 33816, "tid": 1, "ts": 1755986867506447, "dur": 3687, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 33816, "tid": 1, "ts": 1755986867510136, "dur": 37371, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 33816, "tid": 1, "ts": 1755986867547515, "dur": 36185, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 33816, "tid": 6526, "ts": 1755986867701635, "dur": 8, "ph": "X", "name": "", "args": {}}, {"pid": 33816, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867504942, "dur": 6196, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867511139, "dur": 179800, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867512481, "dur": 2203, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867514687, "dur": 1197, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867515887, "dur": 160, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516049, "dur": 9, "ph": "X", "name": "ProcessMessages 20508", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516059, "dur": 21, "ph": "X", "name": "ReadAsync 20508", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516120, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516149, "dur": 21, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516172, "dur": 21, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516196, "dur": 22, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516221, "dur": 19, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516243, "dur": 70, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516317, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516347, "dur": 23, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516373, "dur": 24, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516399, "dur": 22, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516424, "dur": 21, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516446, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516448, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516470, "dur": 30, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516500, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516503, "dur": 29, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516533, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516535, "dur": 29, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516567, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516589, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516611, "dur": 21, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516633, "dur": 23, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516659, "dur": 20, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516681, "dur": 23, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516706, "dur": 20, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516728, "dur": 31, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516762, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516795, "dur": 24, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516821, "dur": 22, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516846, "dur": 24, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516873, "dur": 19, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516895, "dur": 21, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516917, "dur": 22, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516941, "dur": 19, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516963, "dur": 30, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867516996, "dur": 20, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517018, "dur": 20, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517041, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517062, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517086, "dur": 26, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517114, "dur": 20, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517137, "dur": 25, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517164, "dur": 20, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517185, "dur": 20, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517208, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517231, "dur": 21, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517254, "dur": 22, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517278, "dur": 25, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517305, "dur": 26, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517334, "dur": 26, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517361, "dur": 385, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517749, "dur": 69, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517820, "dur": 3, "ph": "X", "name": "ProcessMessages 7057", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517824, "dur": 29, "ph": "X", "name": "ReadAsync 7057", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517855, "dur": 31, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517888, "dur": 27, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517917, "dur": 43, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517962, "dur": 27, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517991, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867517993, "dur": 149, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518145, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518169, "dur": 20, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518191, "dur": 29, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518222, "dur": 19, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518245, "dur": 24, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518272, "dur": 18, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518291, "dur": 18, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518312, "dur": 17, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518331, "dur": 17, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518350, "dur": 17, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518370, "dur": 16, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518388, "dur": 25, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518415, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518436, "dur": 19, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518457, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518478, "dur": 36, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518517, "dur": 21, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518539, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518562, "dur": 18, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518582, "dur": 18, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518604, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518633, "dur": 27, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518663, "dur": 21, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518686, "dur": 19, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518707, "dur": 19, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518728, "dur": 16, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518745, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518766, "dur": 19, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518787, "dur": 18, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518808, "dur": 18, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518828, "dur": 19, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518849, "dur": 16, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518867, "dur": 19, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518888, "dur": 17, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518908, "dur": 26, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518936, "dur": 20, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518959, "dur": 27, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867518987, "dur": 19, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519008, "dur": 19, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519029, "dur": 21, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519052, "dur": 17, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519071, "dur": 1, "ph": "X", "name": "ProcessMessages 117", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519072, "dur": 26, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519100, "dur": 19, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519122, "dur": 21, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519146, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519175, "dur": 19, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519196, "dur": 19, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519217, "dur": 16, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519235, "dur": 17, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519254, "dur": 16, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519272, "dur": 17, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519292, "dur": 18, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519312, "dur": 20, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519333, "dur": 16, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519352, "dur": 17, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519371, "dur": 16, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519389, "dur": 17, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519408, "dur": 18, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519428, "dur": 19, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519448, "dur": 16, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519467, "dur": 20, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519488, "dur": 18, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519507, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519509, "dur": 15, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519526, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519546, "dur": 18, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519567, "dur": 17, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519586, "dur": 20, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519609, "dur": 20, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519631, "dur": 17, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519650, "dur": 18, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519670, "dur": 17, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519690, "dur": 16, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519708, "dur": 17, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519727, "dur": 18, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519747, "dur": 15, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519765, "dur": 17, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519783, "dur": 19, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519805, "dur": 22, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519829, "dur": 17, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519848, "dur": 17, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519867, "dur": 17, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519886, "dur": 17, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519904, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519924, "dur": 17, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519944, "dur": 19, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519965, "dur": 17, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867519985, "dur": 16, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520003, "dur": 17, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520022, "dur": 17, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520042, "dur": 18, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520062, "dur": 17, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520081, "dur": 19, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520102, "dur": 16, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520120, "dur": 17, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520139, "dur": 17, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520158, "dur": 20, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520181, "dur": 16, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520199, "dur": 18, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520219, "dur": 17, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520238, "dur": 17, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520257, "dur": 19, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520278, "dur": 18, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520299, "dur": 18, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520318, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520338, "dur": 18, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520358, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520378, "dur": 18, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520397, "dur": 18, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520417, "dur": 21, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520440, "dur": 17, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520459, "dur": 18, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520479, "dur": 19, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520500, "dur": 18, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520521, "dur": 17, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520540, "dur": 17, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520559, "dur": 18, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520579, "dur": 18, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520599, "dur": 28, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520629, "dur": 23, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520655, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520657, "dur": 27, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520686, "dur": 20, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520708, "dur": 18, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520727, "dur": 18, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520747, "dur": 17, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520766, "dur": 17, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520785, "dur": 17, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520804, "dur": 17, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520823, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520846, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520867, "dur": 18, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520888, "dur": 17, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520908, "dur": 17, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520927, "dur": 18, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520947, "dur": 16, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520965, "dur": 17, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867520984, "dur": 18, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521004, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521024, "dur": 17, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521044, "dur": 18, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521064, "dur": 17, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521083, "dur": 17, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521102, "dur": 17, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521122, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521140, "dur": 17, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521159, "dur": 19, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521180, "dur": 17, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521199, "dur": 17, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521218, "dur": 18, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521238, "dur": 18, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521258, "dur": 27, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521287, "dur": 18, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521308, "dur": 20, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521330, "dur": 17, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521349, "dur": 17, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521368, "dur": 19, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521389, "dur": 16, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521407, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521409, "dur": 17, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521428, "dur": 19, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521449, "dur": 17, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521468, "dur": 17, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521487, "dur": 19, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521509, "dur": 27, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521538, "dur": 20, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521561, "dur": 20, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521582, "dur": 18, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521602, "dur": 19, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521623, "dur": 52, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521682, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867521686, "dur": 410, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522100, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522131, "dur": 186, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522319, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522354, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522389, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522391, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522444, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522446, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522500, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522502, "dur": 52, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522558, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522561, "dur": 50, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522613, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522616, "dur": 41, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522662, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522666, "dur": 58, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522728, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522731, "dur": 56, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522790, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522793, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522829, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522831, "dur": 47, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522882, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522885, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522930, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522934, "dur": 40, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522977, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867522980, "dur": 47, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523030, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523034, "dur": 59, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523096, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523099, "dur": 55, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523156, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523160, "dur": 49, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523212, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523215, "dur": 51, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523269, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523272, "dur": 42, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523316, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523319, "dur": 40, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523361, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523364, "dur": 42, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523409, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523411, "dur": 46, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523461, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523464, "dur": 43, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523510, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523514, "dur": 118, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523635, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523637, "dur": 52, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523692, "dur": 3, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523707, "dur": 50, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523762, "dur": 204, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867523969, "dur": 62, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867524034, "dur": 5, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867524041, "dur": 32, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867524077, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867524079, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867524113, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867524143, "dur": 8637, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867532786, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867532824, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867532827, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867532873, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867532875, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867532912, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867532942, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867533035, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867533058, "dur": 643, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867533717, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867533719, "dur": 267, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867533993, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867533995, "dur": 206, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867534217, "dur": 280, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867534581, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867534617, "dur": 74, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867534726, "dur": 121, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867534951, "dur": 418, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867535382, "dur": 302, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867535694, "dur": 3, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867535698, "dur": 92, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867535866, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867535973, "dur": 160, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867536155, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867536157, "dur": 2854, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867539016, "dur": 4, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867539021, "dur": 69, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867539093, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867539118, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867539147, "dur": 303, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867539454, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867539495, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867539497, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867539546, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867539582, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867539609, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867539702, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867539728, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867539730, "dur": 347, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867540079, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867540120, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867540172, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867540198, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867540221, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867540315, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867540335, "dur": 1095, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867541434, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867541468, "dur": 636, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867542108, "dur": 119621, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867661743, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867661749, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867661787, "dur": 1434, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867663223, "dur": 3118, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867666346, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867666348, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867666389, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867666392, "dur": 1810, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867668206, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867668251, "dur": 12, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867668264, "dur": 13995, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867682265, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867682267, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867682292, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867682294, "dur": 950, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867683248, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867683288, "dur": 18, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867683308, "dur": 755, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867684067, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867684098, "dur": 358, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 33816, "tid": 12884901888, "ts": 1755986867684459, "dur": 5838, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 33816, "tid": 6526, "ts": 1755986867701644, "dur": 444, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 33816, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 33816, "tid": 8589934592, "ts": 1755986867502828, "dur": 80904, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 33816, "tid": 8589934592, "ts": 1755986867583735, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 33816, "tid": 8589934592, "ts": 1755986867583739, "dur": 1010, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 33816, "tid": 6526, "ts": 1755986867702089, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 33816, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 33816, "tid": 4294967296, "ts": 1755986867487036, "dur": 204709, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 33816, "tid": 4294967296, "ts": 1755986867490821, "dur": 7157, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 33816, "tid": 4294967296, "ts": 1755986867691886, "dur": 3254, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 33816, "tid": 4294967296, "ts": 1755986867693856, "dur": 75, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 33816, "tid": 4294967296, "ts": 1755986867695205, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 33816, "tid": 6526, "ts": 1755986867702095, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1755986867509432, "dur": 1467, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755986867510910, "dur": 349, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755986867511345, "dur": 630, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755986867512116, "dur": 262, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_C2B2B5EFE7050494.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755986867513422, "dur": 942, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_B0863ADC38A40DC9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755986867515360, "dur": 1049, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1755986867518118, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755986867512014, "dur": 9969, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755986867521990, "dur": 161695, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755986867683686, "dur": 276, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755986867683993, "dur": 68, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755986867684061, "dur": 114, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755986867684416, "dur": 1273, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1755986867511741, "dur": 10258, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867522006, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E4EBE1DEA4B9DBF9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755986867522845, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867522925, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_080CB5314CBF4724.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755986867523080, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8065517B5A536D03.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755986867523257, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_9CCBC171CB8C01C8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755986867523394, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_BB73E5A042403DAF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755986867523488, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_BB73E5A042403DAF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755986867524022, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1755986867524330, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867525403, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867526284, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867527347, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867528252, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867529470, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867530348, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867531207, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867532078, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867532914, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867533167, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867533222, "dur": 813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867534035, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867534548, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867534659, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755986867534854, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867534912, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755986867535590, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867535840, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755986867536017, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755986867536513, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867536726, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867536982, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867537108, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867537474, "dur": 981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867538455, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867538569, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867539138, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867539289, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867539870, "dur": 621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867540491, "dur": 44617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867586290, "dur": 205, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.1f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1755986867585109, "dur": 1391, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755986867586501, "dur": 97211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867511798, "dur": 10278, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867522078, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_446E57718A74AE8B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755986867522953, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867523016, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_F270133E58124F18.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755986867523102, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867523228, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_F7EA645C15EDC022.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755986867523344, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_71DE750A6D09CA7B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755986867523442, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_71DE750A6D09CA7B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755986867523600, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755986867523657, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755986867523850, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755986867524032, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1755986867524252, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867525381, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867526312, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867527444, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867528404, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867529610, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867530503, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867531386, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867532268, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867532882, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867533253, "dur": 809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867534062, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867534569, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867534689, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867535583, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867535707, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867535860, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867536141, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867536233, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755986867536471, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755986867536894, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867537087, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867537554, "dur": 920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867538475, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867538587, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867539062, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867539304, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867539874, "dur": 615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755986867540525, "dur": 143316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867511783, "dur": 10315, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867522099, "dur": 982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_EB9810B219936736.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755986867523131, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_BA4F652EDE8ADBA9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755986867523353, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_4CE88DA2C6FA0DDC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755986867523480, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_56F11C16920C2AB4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755986867523542, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867523815, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1755986867524111, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755986867524227, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755986867524463, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867525603, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867526623, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867527633, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867528502, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867529789, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867530671, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867531522, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867532391, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867532501, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867533131, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867533218, "dur": 806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867534071, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867534576, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867534692, "dur": 1014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867535706, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867535894, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867536175, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867536259, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867536373, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867536501, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867536683, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867536947, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867537110, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867537484, "dur": 974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867538458, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867538571, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867539123, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867539320, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867539906, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755986867540535, "dur": 143208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867512664, "dur": 9366, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867522031, "dur": 902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_943D1566EB36197C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755986867523151, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_88D8DDE2BF366D61.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755986867523361, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_88D8DDE2BF366D61.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755986867523546, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9ADCC66C60352D2B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755986867523814, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1755986867524153, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755986867524263, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867525312, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867526221, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867527334, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867528259, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867529494, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867530351, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867531233, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867532130, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867533034, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867533135, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867533255, "dur": 819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867534074, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867534578, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867534698, "dur": 996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867535695, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867535868, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867536237, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867536356, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867536417, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867536480, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867536678, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867536937, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867537100, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867537494, "dur": 963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867538457, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867538570, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867539130, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867539319, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867539891, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755986867540520, "dur": 143338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867511873, "dur": 10181, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867522055, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C124AD4B97AA3982.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755986867522995, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_B0863ADC38A40DC9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755986867523097, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_F9E2212BE69EFFE3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755986867523361, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_3BECFE23642E1E2A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755986867523487, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755986867523724, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755986867523854, "dur": 313, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755986867524254, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867525299, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867526159, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867527256, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867528187, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867529499, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867530313, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867531198, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867532021, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867532848, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867533248, "dur": 806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867534055, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867534563, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867534678, "dur": 905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867535702, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867535864, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867536149, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867536230, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755986867536462, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755986867536920, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867537135, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867537463, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755986867537588, "dur": 847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755986867538435, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867538615, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755986867538729, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755986867539178, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867539342, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755986867539446, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755986867539754, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867539933, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755986867540063, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755986867540358, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755986867540566, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755986867540674, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755986867542444, "dur": 119617, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755986867666556, "dur": 16024, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1755986867666536, "dur": 16046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1755986867682598, "dur": 1012, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1755986867511905, "dur": 10147, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867522054, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_20112DFB1B9A6681.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755986867522925, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867522990, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B76ACB483990A7B3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755986867523125, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867523260, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_9520DE7000879E41.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755986867523348, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867523407, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_B03107865E80E25E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755986867523672, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755986867524011, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867524072, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755986867524181, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867524337, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867525443, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867526368, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867527288, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867528333, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867529515, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867530433, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867531310, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867531969, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867532832, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867533254, "dur": 804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867534058, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867534568, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867534708, "dur": 879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867535672, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867535892, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867536184, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867536272, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867536362, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867536450, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867536705, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867536964, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867537140, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867537438, "dur": 998, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867538480, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867538558, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867539058, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867539302, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867539921, "dur": 626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755986867540547, "dur": 143175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867511945, "dur": 10122, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867522089, "dur": 644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0DAAC2D6CA6D2720.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755986867522993, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_558CAE984AE1D1A5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755986867523102, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_128BE6A580A94DC6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755986867523241, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867523397, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B40F3C4651EACB98.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755986867523457, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867523529, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_097DDEAFECA28810.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755986867523581, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867523678, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1755986867523828, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755986867524083, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755986867524267, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867525352, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867526208, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867527205, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867528212, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867529461, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867530319, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867531224, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867532071, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867532887, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867533243, "dur": 813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867534057, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867534562, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867534689, "dur": 889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867535665, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867535756, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867535873, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867536161, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867536251, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867536415, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867536476, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867536672, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867536939, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867537091, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867537546, "dur": 929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867538475, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867538596, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867539032, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867539315, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867539899, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755986867540528, "dur": 143262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867512030, "dur": 10062, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867522095, "dur": 940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_D83BBE6D171D2E8B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755986867523037, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867523107, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1CF8F8636ADD1698.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755986867523253, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867523387, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867523505, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755986867523602, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755986867523689, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867523745, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1755986867523826, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867523891, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867524018, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1755986867524124, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755986867524285, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867525336, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867526281, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867527270, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867528230, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867529418, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867530319, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867531199, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867532105, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867532895, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867533178, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867533232, "dur": 811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867534043, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867534554, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867534666, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755986867534899, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755986867535511, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867535683, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867535857, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867536142, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867536234, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755986867536391, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755986867536771, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867536944, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867537104, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867537511, "dur": 962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867538473, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867538584, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867539069, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867539307, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867539871, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755986867540535, "dur": 143291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867512903, "dur": 9103, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867522007, "dur": 811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B3B4C94707F7A218.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755986867522972, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_47FD15CC38401525.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755986867523051, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867523154, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6583F22C0A896B6F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755986867523328, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6583F22C0A896B6F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755986867523412, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_81ADD57237B7A215.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755986867523511, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_3D53C0654B9396F1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755986867523609, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_3D53C0654B9396F1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755986867523776, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1755986867524024, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1755986867524304, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867525405, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867526297, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867527218, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867528232, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867529435, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867530261, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867531129, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867531967, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867532824, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867533241, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867534051, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867534561, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867534678, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755986867534927, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755986867535556, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867535853, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867535913, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867536167, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867536256, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867536392, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867536463, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867536702, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867536956, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867537077, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867537461, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867538451, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867538564, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867539019, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867539282, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867539349, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755986867539482, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755986867539743, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867539926, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867540498, "dur": 126043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755986867666582, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1755986867666542, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1755986867666712, "dur": 1851, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1755986867668567, "dur": 15119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755986867512115, "dur": 9935, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755986867522051, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_6B03EF9A2B2BFBD3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755986867522846, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755986867522919, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_71130C2744ADE79E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755986867523029, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_886CDD09B49D7AF9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755986867523091, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755986867523239, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E38ED73995C25B9E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755986867523332, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E38ED73995C25B9E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755986867523408, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_C2B2B5EFE7050494.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755986867523458, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755986867523525, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_C2B2B5EFE7050494.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755986867523797, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755986867523947, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755986867524015, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755986867524135, "dur": 8964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755986867533100, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755986867533281, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755986867533391, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755986867533922, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755986867534088, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755986867534181, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755986867534692, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755986867534894, "dur": 1129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755986867536024, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755986867536255, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755986867536416, "dur": 891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755986867537307, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755986867537457, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755986867537572, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755986867538330, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755986867538501, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755986867538603, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755986867538909, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755986867539050, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755986867539297, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755986867539888, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755986867540518, "dur": 143356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867512476, "dur": 9566, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867522043, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_51DEFFE41CB8BCA4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755986867522964, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_EFBDB924F7F1B25C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755986867523058, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_EFBDB924F7F1B25C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755986867523142, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D8EA2BB5196147D6.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755986867523272, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867523414, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_0B128FF1BBAAC47E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755986867523507, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755986867523594, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1755986867523836, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1755986867524080, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1755986867524323, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867525467, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867526399, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867527394, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867528426, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867529690, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867530567, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867531440, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867532311, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867532569, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867533174, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867533235, "dur": 811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867534046, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867534558, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867534682, "dur": 900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867535676, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867535881, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867536157, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867536246, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867536482, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867536679, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867536985, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867537101, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867537502, "dur": 958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867538460, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867538574, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867539115, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867539318, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867539902, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755986867540531, "dur": 143245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867512516, "dur": 9544, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867522063, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_DA0ED1A2C90566BA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755986867522913, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867522970, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_D2ABA6F656B34D47.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755986867523039, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867523163, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_2CD26214566796B4.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755986867523474, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755986867523608, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755986867523722, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755986867523946, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1755986867524258, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867525463, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867526351, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867527508, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867528441, "dur": 1239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867529680, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867530599, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867531489, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867532456, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867533133, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867533231, "dur": 809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867534040, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867534553, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867534685, "dur": 896, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867535628, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867535688, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867535870, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867536152, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867536248, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867536485, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867536680, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867536976, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867537098, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867537517, "dur": 948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867538465, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867538577, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867539091, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867539310, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867539879, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755986867540507, "dur": 143169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867512579, "dur": 9479, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867522061, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_94137FF513C48ABF.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755986867522931, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_D52594234CD03E41.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755986867522997, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867523098, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_20E9E08310ABADB8.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755986867523364, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_6A4766F7572387DA.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755986867523548, "dur": 407, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_18E07CEE712070C7.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755986867523963, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755986867524103, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755986867524180, "dur": 8825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755986867533006, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867533173, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867533225, "dur": 811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867534037, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867534583, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867534671, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755986867534909, "dur": 1293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755986867536203, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867536413, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755986867536597, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755986867537000, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867537164, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867537428, "dur": 1011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867538439, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867538555, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867539022, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867539284, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867539869, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867540493, "dur": 46014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755986867586507, "dur": 97192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867512602, "dur": 9437, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867522066, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_C25CFDCCBD0E71F0.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755986867522841, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867522949, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_DA76F58CBF310F62.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755986867523001, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867523052, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_DA76F58CBF310F62.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755986867523156, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_43979216E7A92FE9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755986867523442, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3352A694B6772687.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755986867523656, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867523710, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1755986867523872, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1755986867524081, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1755986867524270, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867525400, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867526345, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867527381, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867528362, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867529630, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867530519, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867531382, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867532246, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867532988, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867533146, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867533215, "dur": 813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867534028, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867534573, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867534707, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867535597, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867535681, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867535869, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867536150, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867536242, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867536405, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867536474, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867536720, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867536960, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867537080, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867537570, "dur": 907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867538477, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867538591, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867539047, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867539295, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867539849, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867539905, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755986867540534, "dur": 143224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867512634, "dur": 9392, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867522028, "dur": 851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_B16CC6C88F056D62.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755986867522973, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867523133, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_0548B6350D93892B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755986867523255, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867523345, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_36F2911304CC020F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755986867523543, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1755986867523765, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867523883, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1755986867523990, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1755986867524065, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867524193, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1755986867524280, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867525373, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867526220, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867527165, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867528179, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867529408, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867530304, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867531249, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867532089, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867532751, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867533233, "dur": 811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867534045, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867534557, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867534678, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755986867534869, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867534924, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755986867535556, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867535863, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867536140, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867536235, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867536358, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867536498, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867536698, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867536955, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867537073, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867537424, "dur": 1013, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867538478, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867538590, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867539054, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867539300, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867539887, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755986867540515, "dur": 143398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867511829, "dur": 10227, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867522071, "dur": 1027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_1C4A8B1B6898FD4D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755986867523099, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867523230, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_3FD29ACDA5717940.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755986867523348, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_448E988461C74DB0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755986867523462, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867523561, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1755986867523679, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1755986867524077, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1755986867524151, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1755986867524268, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867525458, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867526458, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867527433, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867528384, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867529627, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867530428, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867531418, "dur": 1085, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@80da3b035d6b\\Runtime\\UGUI\\UI\\Core\\AnimationTriggers.cs"}}, {"pid": 12345, "tid": 16, "ts": 1755986867531290, "dur": 1596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867532886, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867533220, "dur": 802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867534075, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867534543, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867534657, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755986867534926, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755986867535580, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867535821, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755986867536021, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755986867536516, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867536731, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867536973, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867537091, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867537532, "dur": 936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867538469, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867538579, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867539083, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867539311, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867539892, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755986867540522, "dur": 143345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867512703, "dur": 9329, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867522033, "dur": 1029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_3F0948DC6B40CBC0.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755986867523143, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F15DE0412BCD77BB.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755986867523319, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F15DE0412BCD77BB.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755986867523473, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_88B5229BB905F051.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755986867523601, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1755986867523831, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1755986867524086, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1755986867524137, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1755986867524260, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867525384, "dur": 1241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867526626, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867527675, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867528517, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867529677, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867530907, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867531787, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867532727, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867533246, "dur": 806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867534052, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867534565, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867534694, "dur": 944, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867535664, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867535822, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867535872, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867536153, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867536245, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867536489, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867536685, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867536953, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867537076, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867537463, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867538454, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867538567, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867539052, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867539299, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867539884, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755986867540512, "dur": 143389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867512725, "dur": 9357, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867522084, "dur": 910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4C8A9319ED1CA3AE.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755986867522995, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867523051, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4C8A9319ED1CA3AE.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755986867523122, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_AFDD633454108E81.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755986867523373, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_AFDD633454108E81.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755986867523608, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1755986867524140, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1755986867524288, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867525444, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867527878, "dur": 649, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Utilities\\PluginPaths.cs"}}, {"pid": 12345, "tid": 18, "ts": 1755986867526778, "dur": 1749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867528527, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867529684, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867530657, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867531530, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867532464, "dur": 670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867533134, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867533267, "dur": 764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867534031, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867534547, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867534653, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755986867534888, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1755986867535458, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867535664, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867535757, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867535854, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867536061, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867536145, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867536238, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867536414, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755986867536667, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1755986867537035, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867537162, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867537447, "dur": 991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867538438, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867538557, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867539020, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867539283, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867539917, "dur": 621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755986867540539, "dur": 143193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867512744, "dur": 9346, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867522091, "dur": 1009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_C223B6F3143702D1.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755986867523101, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867523244, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_CBCEC9956E2477BD.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755986867523329, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_CBCEC9956E2477BD.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755986867523524, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_B3E44AC0B8D941C7.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755986867523617, "dur": 453, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_B3E44AC0B8D941C7.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755986867524242, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1755986867524296, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867525404, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867526283, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867527247, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867528228, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867529489, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867530311, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867531189, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867532021, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867532838, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867533244, "dur": 823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867534067, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867534572, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867534691, "dur": 979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867535670, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867535901, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867536164, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867536252, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867536401, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867536473, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867536690, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867536950, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867537072, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867537460, "dur": 988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867538448, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867538561, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867539020, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867539286, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867539894, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755986867540524, "dur": 143283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867512763, "dur": 9274, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867522045, "dur": 918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_12BCE5693E524B55.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1755986867523020, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6941CE050E1CF5A2.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1755986867523084, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867523253, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_47D5F37EEAD56BE0.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1755986867523464, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867523730, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1755986867523881, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1755986867523994, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1755986867524291, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867525385, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867526258, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867527172, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867528181, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867529404, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867530242, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867531140, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867531959, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867532796, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867533239, "dur": 808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867534048, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867534555, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867534667, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1755986867534925, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1755986867535625, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867535832, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867535886, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867536159, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867536251, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867536456, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867536670, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867536972, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867537089, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867537539, "dur": 932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867538472, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867538582, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867539076, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867539308, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867539881, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755986867540508, "dur": 143172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867512782, "dur": 9252, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867522052, "dur": 928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_05BE74699551E17D.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1755986867522981, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867523045, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_A067B7A9CA1EAF47.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1755986867523117, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867523249, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_B2740E25E009BD1A.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1755986867523361, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F129D49E2ADECCC2.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1755986867523462, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867523537, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1755986867523633, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1755986867523853, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1755986867524025, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1755986867524220, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867524282, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867525369, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867526255, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867527120, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867528245, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867529439, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867530286, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867531144, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867532026, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867532878, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867533257, "dur": 807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867534064, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867534571, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867534695, "dur": 931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867535665, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867535861, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867536139, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867536236, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867536357, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867536450, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867536668, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867536723, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867536967, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867537093, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867537524, "dur": 943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867538467, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867538575, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867539099, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867539313, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867539873, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755986867540504, "dur": 143174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867512801, "dur": 9220, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867522033, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_EA0B5218CB807CE8.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755986867522934, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_EC805490FEC13931.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755986867522989, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867523096, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867523310, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_AF814272D3883F57.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755986867523696, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1755986867523936, "dur": 242, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1755986867524244, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867525342, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867526251, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867527458, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867528475, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867529706, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867530613, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867531468, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867532472, "dur": 659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867533132, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867533216, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867534026, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867534542, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867534656, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755986867534939, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1755986867535521, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867535865, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867536174, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867536257, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867536382, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867536466, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867536675, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867536938, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867537063, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867537129, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867537465, "dur": 998, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867538464, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867538575, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867539107, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867539316, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867539885, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755986867540514, "dur": 143374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867512835, "dur": 9181, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867522019, "dur": 819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_B9DC0DB8A04DAC4C.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755986867522989, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_E57DD11F6513DD1B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755986867523094, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_AF06999FBA4544CA.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755986867523265, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_CEA60A69666C96C6.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755986867523470, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_EF1B9BDC36CA0637.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755986867523575, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1755986867523678, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1755986867523980, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867524033, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1755986867524326, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867525421, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867526304, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867527310, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867528348, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867529641, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867530505, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867531392, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867532265, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867532941, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867533156, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867533217, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867534027, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867534540, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867534660, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755986867534892, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1755986867535587, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867535792, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755986867536015, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1755986867536801, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867536993, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867537068, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867537423, "dur": 1017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867538440, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867538592, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867539039, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867539292, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867539876, "dur": 626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755986867540502, "dur": 143180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867512856, "dur": 9156, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867522013, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0903FEE7BAED8E66.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755986867522850, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867523060, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867523137, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_A964C83E710562E0.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755986867523350, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867523498, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_30460E0C3839B364.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755986867523656, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1755986867523842, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1755986867523996, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1755986867524238, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1755986867524399, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867525575, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867526464, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867527478, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867528420, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867529709, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867530589, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867531442, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867532343, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867532488, "dur": 641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867533228, "dur": 811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867534039, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867534549, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867534654, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755986867534893, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755986867534945, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1755986867535491, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867535681, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755986867535850, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1755986867536255, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867536490, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867536546, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867536693, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867536957, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867537079, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867537562, "dur": 883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867538445, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867538596, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867539024, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867539324, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867539894, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755986867540527, "dur": 143287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755986867688536, "dur": 1632, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 33816, "tid": 6526, "ts": 1755986867702436, "dur": 1612, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 33816, "tid": 6526, "ts": 1755986867704099, "dur": 1662, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 33816, "tid": 6526, "ts": 1755986867699804, "dur": 6714, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}