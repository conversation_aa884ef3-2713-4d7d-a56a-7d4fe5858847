fileFormatVersion: 2
guid: b9842570bb8fe3243a4732298b1d5a27
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Armature
    100002: BezierCurve
    100004: Body
    100006: Gun
    100008: L0
    100010: L0_b
    100012: L0_b_end
    100014: L1
    100016: L1_b
    100018: L1_b_end
    100020: L2
    100022: L2_b
    100024: L2_b_end
    100026: L3
    100028: L3_b
    100030: L3_b_end
    100032: L4
    100034: L4_b
    100036: L4_b_end
    100038: L5
    100040: L5_b
    100042: L5_b_end
    100044: L6
    100046: L6_b
    100048: L6_b_end
    100050: L7
    100052: L7_b
    100054: L7_b.001
    100056: L7_b.001_end
    100058: L7_b_end
    100060: R0
    100062: R0_b
    100064: R0_b_end
    100066: R1
    100068: R1_b
    100070: R1_b_end
    100072: R2
    100074: R2_b
    100076: R2_b_end
    100078: R3
    100080: R3_b
    100082: R3_b_end
    100084: R4
    100086: R4_b
    100088: R4_b_end
    100090: R5
    100092: R5_b
    100094: R5_b_end
    100096: R6
    100098: R6b
    100100: R6b_end
    100102: R7
    100104: root
    100106: //RootNode
    100108: Trucks
    100110: Turret
    100112: TurretRiffle
    400000: Armature
    400002: BezierCurve
    400004: Body
    400006: Gun
    400008: L0
    400010: L0_b
    400012: L0_b_end
    400014: L1
    400016: L1_b
    400018: L1_b_end
    400020: L2
    400022: L2_b
    400024: L2_b_end
    400026: L3
    400028: L3_b
    400030: L3_b_end
    400032: L4
    400034: L4_b
    400036: L4_b_end
    400038: L5
    400040: L5_b
    400042: L5_b_end
    400044: L6
    400046: L6_b
    400048: L6_b_end
    400050: L7
    400052: L7_b
    400054: L7_b.001
    400056: L7_b.001_end
    400058: L7_b_end
    400060: R0
    400062: R0_b
    400064: R0_b_end
    400066: R1
    400068: R1_b
    400070: R1_b_end
    400072: R2
    400074: R2_b
    400076: R2_b_end
    400078: R3
    400080: R3_b
    400082: R3_b_end
    400084: R4
    400086: R4_b
    400088: R4_b_end
    400090: R5
    400092: R5_b
    400094: R5_b_end
    400096: R6
    400098: R6b
    400100: R6b_end
    400102: R7
    400104: root
    400106: //RootNode
    400108: Trucks
    400110: Turret
    400112: TurretRiffle
    2100000: Trucks
    2100002: Palette
    2300000: BezierCurve
    2300002: Body
    2300004: Gun
    2300006: L0
    2300008: L1
    2300010: L2
    2300012: L3
    2300014: L4
    2300016: L5
    2300018: L6
    2300020: L7
    2300022: R0
    2300024: R1
    2300026: R2
    2300028: R3
    2300030: R4
    2300032: R5
    2300034: R6
    2300036: R7
    2300038: Turret
    2300040: TurretRiffle
    3300000: BezierCurve
    3300002: Body
    3300004: Gun
    3300006: L0
    3300008: L1
    3300010: L2
    3300012: L3
    3300014: L4
    3300016: L5
    3300018: L6
    3300020: L7
    3300022: R0
    3300024: R1
    3300026: R2
    3300028: R3
    3300030: R4
    3300032: R5
    3300034: R6
    3300036: R7
    3300038: Turret
    3300040: TurretRiffle
    4300000: Trucks
    4300002: R0
    4300004: R1
    4300006: R2
    4300008: R3
    4300010: R4
    4300012: R5
    4300014: R6
    4300016: R7
    4300018: L7
    4300020: L6
    4300022: L5
    4300024: L4
    4300026: L3
    4300028: L2
    4300030: TurretRiffle
    4300032: L1
    4300034: L0
    4300036: BezierCurve
    4300038: Gun
    4300040: Turret
    4300042: Body
    6400000: BezierCurve
    6400002: Body
    6400004: Gun
    6400006: L0
    6400008: L1
    6400010: L2
    6400012: L3
    6400014: L4
    6400016: L5
    6400018: L6
    6400020: L7
    6400022: R0
    6400024: R1
    6400026: R2
    6400028: R3
    6400030: R4
    6400032: R5
    6400034: R6
    6400036: R7
    6400038: Turret
    6400040: TurretRiffle
    9500000: //RootNode
    13700000: Trucks
    2186277476908879412: ImportLogs
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 0
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 122675
  packageName: Stylized Military Vehicles Pack
  packageVersion: 1.02
  assetPath: Assets/StylizedMilitaryVehiclesPack/Models/Vehicles/Tank1/Tank1.fbx
  uploadId: 338346
