fileFormatVersion: 2
guid: 863c651e1be219845a72e4082a6ec3df
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: AmmoBox
    100002: AmmoBox.001
    100004: AmmoBox.002
    100006: AmmoBox.003
    100008: AmmoBox.004
    100010: AmmoBox.005
    100012: AmmoBox.006
    100014: AmmoBox.007
    100016: AmmoBox.008
    100018: AmmoBox.009
    100020: BL
    100022: BR
    100024: //RootNode
    100026: CarBody
    100028: CarGun
    100030: FL
    100032: FR
    100034: rudder
    100036: WeaponRotator
    400000: AmmoBox
    400002: AmmoBox.001
    400004: AmmoBox.002
    400006: AmmoBox.003
    400008: AmmoBox.004
    400010: AmmoBox.005
    400012: AmmoBox.006
    400014: AmmoBox.007
    400016: AmmoBox.008
    400018: AmmoBox.009
    400020: BL
    400022: BR
    400024: //RootNode
    400026: CarBody
    400028: CarGun
    400030: FL
    400032: FR
    400034: rudder
    400036: WeaponRotator
    2100000: No Name
    2100002: Palette
    2300000: AmmoBox
    2300002: AmmoBox.001
    2300004: AmmoBox.002
    2300006: AmmoBox.003
    2300008: AmmoBox.004
    2300010: AmmoBox.005
    2300012: AmmoBox.006
    2300014: AmmoBox.007
    2300016: AmmoBox.008
    2300018: AmmoBox.009
    2300020: BL
    2300022: BR
    2300024: CarBody
    2300026: CarGun
    2300028: FL
    2300030: FR
    2300032: rudder
    2300034: WeaponRotator
    3300000: AmmoBox
    3300002: AmmoBox.001
    3300004: AmmoBox.002
    3300006: AmmoBox.003
    3300008: AmmoBox.004
    3300010: AmmoBox.005
    3300012: AmmoBox.006
    3300014: AmmoBox.007
    3300016: AmmoBox.008
    3300018: AmmoBox.009
    3300020: BL
    3300022: BR
    3300024: CarBody
    3300026: CarGun
    3300028: FL
    3300030: FR
    3300032: rudder
    3300034: WeaponRotator
    4300000: CarGun
    4300002: WeaponRotator
    4300004: AmmoBox
    4300006: AmmoBox.001
    4300008: AmmoBox.006
    4300010: AmmoBox.007
    4300012: AmmoBox.005
    4300014: AmmoBox.004
    4300016: AmmoBox.002
    4300018: AmmoBox.003
    4300020: AmmoBox.008
    4300022: AmmoBox.009
    4300024: BR
    4300026: FR
    4300028: BL
    4300030: FL
    4300032: rudder
    4300034: CarBody
    6400000: AmmoBox
    6400002: AmmoBox.001
    6400004: AmmoBox.002
    6400006: AmmoBox.003
    6400008: AmmoBox.004
    6400010: AmmoBox.005
    6400012: AmmoBox.006
    6400014: AmmoBox.007
    6400016: AmmoBox.008
    6400018: AmmoBox.009
    6400020: BL
    6400022: BR
    6400024: CarBody
    6400026: CarGun
    6400028: FL
    6400030: FR
    6400032: rudder
    6400034: WeaponRotator
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 122675
  packageName: Stylized Military Vehicles Pack
  packageVersion: 1.02
  assetPath: Assets/StylizedMilitaryVehiclesPack/Models/Vehicles/Car2/Car2.fbx
  uploadId: 338346
