fileFormatVersion: 2
guid: 907612da75e965845b5a476fdef9108c
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Armature
    100002: BL1
    100004: BL1_b
    100006: BL1_b_end
    100008: BL2
    100010: BL2_b
    100012: BL2_b_end
    100014: BL3
    100016: BL3_b
    100018: BL3_b_end
    100020: BL4
    100022: BL4_b
    100024: BL4_b_end
    100026: Body
    100028: BR1
    100030: BR1_b
    100032: BR1_b_end
    100034: BR2
    100036: BR2_b
    100038: BR2_b_end
    100040: BR3
    100042: BR3_b
    100044: BR3_b_end
    100046: BR4
    100048: BR4_b
    100050: BR4_b_end
    100052: //RootNode
    100054: FL0
    100056: FL1
    100058: FL1_b
    100060: FL1_b_end
    100062: FL2
    100064: FL2_b
    100066: FL2_b_end
    100068: FL3
    100070: FL3_b
    100072: FL3_b_end
    100074: FL4_b
    100076: FL4_b_end
    100078: FR1
    100080: FR1_b
    100082: FR1_b_end
    100084: FR2
    100086: FR2_b
    100088: FR2_b_end
    100090: FR3
    100092: FR3_b
    100094: FR3_b_end
    100096: FR4
    100098: FR4_b
    100100: FR4_b_end
    100102: Gun
    100104: root
    100106: TFL1
    100108: TFL2
    100110: TFL3
    100112: TFR1
    100114: TFR2
    100116: TFR3
    100118: Trucks
    100120: Turret
    400000: Armature
    400002: BL1
    400004: BL1_b
    400006: BL1_b_end
    400008: BL2
    400010: BL2_b
    400012: BL2_b_end
    400014: BL3
    400016: BL3_b
    400018: BL3_b_end
    400020: BL4
    400022: BL4_b
    400024: BL4_b_end
    400026: Body
    400028: BR1
    400030: BR1_b
    400032: BR1_b_end
    400034: BR2
    400036: BR2_b
    400038: BR2_b_end
    400040: BR3
    400042: BR3_b
    400044: BR3_b_end
    400046: BR4
    400048: BR4_b
    400050: BR4_b_end
    400052: //RootNode
    400054: FL0
    400056: FL1
    400058: FL1_b
    400060: FL1_b_end
    400062: FL2
    400064: FL2_b
    400066: FL2_b_end
    400068: FL3
    400070: FL3_b
    400072: FL3_b_end
    400074: FL4_b
    400076: FL4_b_end
    400078: FR1
    400080: FR1_b
    400082: FR1_b_end
    400084: FR2
    400086: FR2_b
    400088: FR2_b_end
    400090: FR3
    400092: FR3_b
    400094: FR3_b_end
    400096: FR4
    400098: FR4_b
    400100: FR4_b_end
    400102: Gun
    400104: root
    400106: TFL1
    400108: TFL2
    400110: TFL3
    400112: TFR1
    400114: TFR2
    400116: TFR3
    400118: Trucks
    400120: Turret
    2100000: Trucks
    2100002: Palette
    2300000: BL1
    2300002: BL2
    2300004: BL3
    2300006: BL4
    2300008: Body
    2300010: BR1
    2300012: BR2
    2300014: BR3
    2300016: BR4
    2300018: FL0
    2300020: FL1
    2300022: FL2
    2300024: FL3
    2300026: FR1
    2300028: FR2
    2300030: FR3
    2300032: FR4
    2300034: Gun
    2300036: TFL1
    2300038: TFL2
    2300040: TFL3
    2300042: TFR1
    2300044: TFR2
    2300046: TFR3
    2300048: Turret
    3300000: BL1
    3300002: BL2
    3300004: BL3
    3300006: BL4
    3300008: Body
    3300010: BR1
    3300012: BR2
    3300014: BR3
    3300016: BR4
    3300018: FL0
    3300020: FL1
    3300022: FL2
    3300024: FL3
    3300026: FR1
    3300028: FR2
    3300030: FR3
    3300032: FR4
    3300034: Gun
    3300036: TFL1
    3300038: TFL2
    3300040: TFL3
    3300042: TFR1
    3300044: TFR2
    3300046: TFR3
    3300048: Turret
    4300000: Trucks
    4300002: FR1
    4300004: FR2
    4300006: TFR1
    4300008: FR3
    4300010: FR4
    4300012: BR1
    4300014: BR2
    4300016: BR3
    4300018: BR4
    4300020: TFR2
    4300022: TFR3
    4300024: TFL3
    4300026: TFL2
    4300028: BL4
    4300030: BL3
    4300032: BL2
    4300034: BL1
    4300036: FL3
    4300038: FL2
    4300040: TFL1
    4300042: FL1
    4300044: FL0
    4300046: Gun
    4300048: Turret
    4300050: Body
    6400000: BL1
    6400002: BL2
    6400004: BL3
    6400006: BL4
    6400008: Body
    6400010: BR1
    6400012: BR2
    6400014: BR3
    6400016: BR4
    6400018: FL0
    6400020: FL1
    6400022: FL2
    6400024: FL3
    6400026: FR1
    6400028: FR2
    6400030: FR3
    6400032: FR4
    6400034: Gun
    6400036: TFL1
    6400038: TFL2
    6400040: TFL3
    6400042: TFR1
    6400044: TFR2
    6400046: TFR3
    6400048: Turret
    9500000: //RootNode
    13700000: Trucks
    2186277476908879412: ImportLogs
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 122675
  packageName: Stylized Military Vehicles Pack
  packageVersion: 1.02
  assetPath: Assets/StylizedMilitaryVehiclesPack/Models/Vehicles/FireSupport3/FireSupport3.fbx
  uploadId: 338346
